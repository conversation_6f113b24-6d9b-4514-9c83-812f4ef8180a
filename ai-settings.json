{"dynamicAnalysisItems": [{"id": "vip_community", "displayName": "VIP 1群|一支烟花社区", "timeRange": "yesterday", "groupName": "17281868060@chatroom", "prompt": "请分析这个VIP社区群聊的聊天记录，重点关注：\n\n1. 社区动态和重要公告\n2. 成员互动和价值分享\n3. 产品讨论和用户反馈\n4. 技术交流和经验分享\n5. 社区建设和发展建议\n\n请提供结构化的分析报告，包括：\n- 主要话题和讨论重点\n- 有价值的观点和建议\n- 重要信息和资源分享\n- 社区活跃度和参与情况"}, {"id": "ai_coding_community", "displayName": "一支烟花AI Coding 社群", "timeRange": "yesterday", "groupName": "57020500175@chatroom", "prompt": "请分析这个AI编程社群的聊天记录，重点关注：\n\n1. AI编程技术讨论\n2. 代码分享和问题解决\n3. AI工具和框架使用经验\n4. 项目实践和案例分享\n5. 技术趋势和发展动态\n\n请提供结构化的分析报告，包括：\n- 主要技术话题总结\n- 重要代码片段和解决方案\n- AI工具使用心得和技巧\n- 有价值的学习资源整理"}, {"id": "general_ai_community", "displayName": "通用智能体|一支烟花社区 18群", "timeRange": "yesterday", "groupName": "56316300744@chatroom", "prompt": "请分析这个通用智能体社区群聊的聊天记录，重点关注：\n\n1. 智能体技术讨论和应用\n2. AI产品和工具分享\n3. 行业动态和趋势分析\n4. 实践经验和案例研究\n5. 技术问题和解决方案\n\n请提供结构化的分析报告，包括：\n- 主要AI话题和讨论重点\n- 智能体应用场景和案例\n- 技术观点和专业见解\n- 有价值的工具和资源推荐"}, {"id": "ai_product_locust", "displayName": "【1】AI产品蝗虫团", "timeRange": "yesterday", "groupName": "53431843688@chatroom", "prompt": "请分析这个AI产品蝗虫团群聊的聊天记录，重点关注：\n\n1. AI产品体验和评测\n2. 产品功能和使用技巧\n3. 行业动态和产品对比\n4. 用户反馈和改进建议\n5. 新产品发现和推荐\n\n请提供结构化的分析报告，包括：\n- 主要产品讨论和评价\n- 实用功能和使用心得\n- 产品优缺点分析\n- 值得关注的新产品和趋势"}, {"id": "n8n_learning", "displayName": "n8n学习交流", "timeRange": "yesterday", "groupName": "56040098424@chatroom", "prompt": "请分析这个n8n学习交流群的聊天记录，重点关注：\n\n1. n8n工作流设计和实现\n2. 自动化场景和解决方案\n3. 技术问题和故障排除\n4. 最佳实践和经验分享\n5. 插件和集成应用\n\n请提供结构化的分析报告，包括：\n- 主要工作流案例和应用\n- 技术问题和解决方法\n- 实用技巧和最佳实践\n- 有价值的学习资源和工具"}]}