{"name": "chatlog-web", "version": "2.7.0", "description": "基于Node.js的中文聊天记录查询与AI分析系统", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "ejs": "^3.1.9", "express": "^4.18.2", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "moment": "^2.29.4", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["chatlog", "wechat", "nodejs", "express"], "author": "", "license": "MIT"}