{"groupName": "AI-Native产品&技术交流", "analysisType": "custom", "timeRange": "2025-06-15", "messageCount": 3, "timestamp": "2025-06-16T12:41:12.625Z", "title": "AI-Native产品&技术交流 - 自定义分析", "content": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>AI-Native产品&技术交流 - 2025年06月15日 聊天精华报告</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js\"></script>\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n    <style>\n        :root {\n            --primary-color: #FF7F50;\n            --secondary-color: #FFA07A;\n            --accent-color: #FFD700;\n            --light-bg: #FFF8DC;\n            --dark-text: #5C4033;\n            --light-text: #8B4513;\n        }\n        \n        body {\n            font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", <PERSON><PERSON>, \"Helvetica Neue\", <PERSON><PERSON>, \"Noto Sans SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"WenQuanYi Micro Hei\", sans-serif;\n            background-color: #FFF5EE;\n            color: var(--dark-text);\n            line-height: 1.6;\n            padding: 0;\n            margin: 0;\n        }\n        \n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n            padding: 20px;\n        }\n        \n        header {\n            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n            color: white;\n            padding: 30px 20px;\n            border-radius: 10px;\n            margin-bottom: 30px;\n            box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n            text-align: center;\n        }\n        \n        h1 {\n            font-size: 2.2rem;\n            margin: 0;\n            padding: 0;\n        }\n        \n        h2 {\n            color: var(--primary-color);\n            font-size: 1.8rem;\n            margin-top: 40px;\n            border-bottom: 2px solid var(--secondary-color);\n            padding-bottom: 10px;\n        }\n        \n        h3 {\n            color: var(--light-text);\n            font-size: 1.4rem;\n        }\n        \n        .card {\n            background-color: white;\n            border-radius: 10px;\n            padding: 20px;\n            margin-bottom: 20px;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n            transition: transform 0.3s ease, box-shadow 0.3s ease;\n        }\n        \n        .card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 8px 16px rgba(0,0,0,0.1);\n        }\n        \n        .keyword-tag {\n            display: inline-block;\n            background-color: var(--secondary-color);\n            color: white;\n            padding: 5px 15px;\n            border-radius: 20px;\n            margin: 5px;\n            font-size: 0.9rem;\n            font-weight: 500;\n        }\n        \n        .message-bubble {\n            max-width: 80%;\n            padding: 12px 16px;\n            border-radius: 18px;\n            margin-bottom: 10px;\n            position: relative;\n        }\n        \n        .message-left {\n            background-color: #FFE4B5;\n            margin-right: auto;\n            border-top-left-radius: 5px;\n        }\n        \n        .message-right {\n            background-color: #FFDEAD;\n            margin-left: auto;\n            border-top-right-radius: 5px;\n        }\n        \n        .speaker-info {\n            font-size: 0.8rem;\n            color: var(--light-text);\n            margin-bottom: 5px;\n        }\n        \n        .quote-card {\n            background-color: #FFF0F5;\n            border-left: 4px solid var(--primary-color);\n            padding: 15px;\n            margin-bottom: 15px;\n            border-radius: 5px;\n        }\n        \n        .quote-text {\n            font-style: italic;\n            font-size: 1.1rem;\n        }\n        \n        .quote-author {\n            text-align: right;\n            font-size: 0.9rem;\n            color: var(--light-text);\n        }\n        \n        .stats-container {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 15px;\n            margin-bottom: 30px;\n        }\n        \n        .stat-card {\n            flex: 1;\n            min-width: 200px;\n            background-color: white;\n            border-radius: 10px;\n            padding: 15px;\n            text-align: center;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n        }\n        \n        .stat-value {\n            font-size: 2rem;\n            font-weight: bold;\n            color: var(--primary-color);\n            margin: 10px 0;\n        }\n        \n        .stat-label {\n            font-size: 0.9rem;\n            color: var(--light-text);\n        }\n        \n        .chart-container {\n            height: 300px;\n            margin-bottom: 30px;\n        }\n        \n        @media (max-width: 768px) {\n            .stats-container {\n                flex-direction: column;\n            }\n            \n            h1 {\n                font-size: 1.8rem;\n            }\n            \n            h2 {\n                font-size: 1.5rem;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header>\n            <h1>AI-Native产品&技术交流</h1>\n            <p>2025年06月15日 聊天精华报告</p>\n        </header>\n        \n        <div class=\"stats-container\">\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">3</div>\n                <div class=\"stat-label\">消息总数</div>\n            </div>\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">2</div>\n                <div class=\"stat-label\">有效消息</div>\n            </div>\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">2</div>\n                <div class=\"stat-label\">活跃用户</div>\n            </div>\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">4分钟</div>\n                <div class=\"stat-label\">讨论时长</div>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>核心关键词速览</h2>\n            <div>\n                <span class=\"keyword-tag\">投资人活动</span>\n                <span class=\"keyword-tag\">报名倒计时</span>\n                <span class=\"keyword-tag\">PAG太盟投资</span>\n                <span class=\"keyword-tag\">软银中国</span>\n                <span class=\"keyword-tag\">IDG资本</span>\n                <span class=\"keyword-tag\">高盛</span>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>核心概念关系图</h2>\n            <div class=\"mermaid\">\n                flowchart LR\n                A[投资人活动] --> B[报名倒计时]\n                A --> C[投资机构]\n                C --> D[PAG太盟投资]\n                C --> E[软银中国]\n                C --> F[IDG资本]\n                C --> G[高盛]\n                A --> H[AI/机器人/web3]\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>精华话题聚焦</h2>\n            <h3>投资人活动报名</h3>\n            <p>群内分享了即将举行的投资人活动信息，包括参与的部分投资人名单和活动倒计时提醒。</p>\n            \n            <h4>重要对话节选</h4>\n            <div class=\"message-bubble message-left\">\n                <div class=\"speaker-info\">Big fans of Jazz 20:42:50</div>\n                <div class=\"dialogue-content\">［转］报名倒计时6天‼️<br>【周六投资人活动已报名60+】<br>参与部分投资人名单：PAG太盟投资、软银中国、金浦投资、IDG资本、高盛、兆龙互连、盈确控股、晨熹资本、百联商投、拙朴投资、Saintander Partners（海外）、驼峰资本等等、晶科战投、仓廪资本、浩悦资本、星海控股/华懋科技CVC 、蓝图创投、鹏安基金、民生证券投行部等等。涵盖美元资本、产业资本、上市公司CVC、以及人民币基金；以及AI、机器人、web3相关企业创始人。</div>\n            </div>\n            \n            <div class=\"message-bubble message-right\">\n                <div class=\"speaker-info\">Peng 20:46:08</div>\n                <div class=\"dialogue-content\">私信你了</div>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>活跃用户分析</h2>\n            <div class=\"chart-container\">\n                <canvas id=\"userChart\"></canvas>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>时间分布分析</h2>\n            <div class=\"chart-container\">\n                <canvas id=\"timeChart\"></canvas>\n            </div>\n        </div>\n    </div>\n\n    <script>\n        // 用户活跃度图表\n        const userCtx = document.getElementById('userChart').getContext('2d');\n        new Chart(userCtx, {\n            type: 'doughnut',\n            data: {\n                labels: ['Big fans of Jazz', 'Peng'],\n                datasets: [{\n                    data: [1, 1],\n                    backgroundColor: ['#FF7F50', '#FFA07A'],\n                    borderWidth: 1\n                }]\n            },\n            options: {\n                responsive: true,\n                plugins: {\n                    legend: {\n                        position: 'bottom',\n                    },\n                    title: {\n                        display: true,\n                        text: '用户发言数量分布',\n                        font: {\n                            size: 16\n                        }\n                    }\n                }\n            }\n        });\n        \n        // 时间分布图表\n        const timeCtx = document.getElementById('timeChart').getContext('2d');\n        new Chart(timeCtx, {\n            type: 'bar',\n            data: {\n                labels: ['20:42', '20:43', '20:44', '20:45', '20:46'],\n                datasets: [{\n                    label: '消息数量',\n                    data: [1, 0, 0, 0, 1],\n                    backgroundColor: '#FFA07A',\n                    borderColor: '#FF7F50',\n                    borderWidth: 1\n                }]\n            },\n            options: {\n                responsive: true,\n                scales: {\n                    y: {\n                        beginAtZero: true,\n                        ticks: {\n                            stepSize: 1\n                        }\n                    }\n                },\n                plugins: {\n                    title: {\n                        display: true,\n                        text: '消息时间分布',\n                        font: {\n                            size: 16\n                        }\n                    }\n                }\n            }\n        });\n        \n        // 初始化Mermaid\n        mermaid.initialize({\n            startOnLoad: true,\n            theme: 'default',\n            themeVariables: {\n                primaryColor: '#FFE4B5',\n                nodeBorder: '#FF7F50',\n                lineColor: '#FFA07A',\n                textColor: '#5C4033'\n            }\n        });\n    </script>\n</body>\n</html>", "savedAt": "2025-06-16T12:41:12.625Z"}