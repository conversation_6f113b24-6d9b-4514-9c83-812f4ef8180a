{"title": "[定时] 自定义分析 - AI产品蝗虫团", "groupName": "【1】AI产品蝗虫团", "analysisType": "custom", "timeRange": "2025-06-21~2025-06-21", "messageCount": 500, "isScheduled": true, "content": "```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>【1】AI产品蝗虫团 - 聊天数据分析报告</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js\"></script>\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n    <style>\n        :root {\n            --primary: #FF8C42;\n            --secondary: #FFB563;\n            --accent: #F25F5C;\n            --light: #FFE8D6;\n            --dark: #5C4033;\n            --text: #5C4033;\n            --card-bg: rgba(255, 236, 214, 0.8);\n        }\n        \n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;\n        }\n        \n        body {\n            background: linear-gradient(135deg, #FFF8F0 0%, #FFE8D6 100%);\n            color: var(--text);\n            line-height: 1.6;\n            padding: 20px;\n            min-height: 100vh;\n        }\n        \n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n        }\n        \n        header {\n            text-align: center;\n            padding: 30px 0;\n            margin-bottom: 30px;\n            border-bottom: 2px solid var(--primary);\n        }\n        \n        h1 {\n            color: var(--dark);\n            font-size: 2.8rem;\n            margin-bottom: 10px;\n            text-shadow: 2px 2px 4px rgba(92, 64, 51, 0.1);\n        }\n        \n        .subtitle {\n            color: var(--accent);\n            font-size: 1.2rem;\n            font-weight: 500;\n        }\n        \n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n            gap: 20px;\n            margin-bottom: 40px;\n        }\n        \n        .stat-card {\n            background: var(--card-bg);\n            border-radius: 15px;\n            padding: 25px;\n            text-align: center;\n            box-shadow: 0 8px 20px rgba(242, 95, 92, 0.1);\n            transition: transform 0.3s ease;\n            border: 1px solid rgba(255, 140, 66, 0.2);\n        }\n        \n        .stat-card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 12px 25px rgba(242, 95, 92, 0.15);\n        }\n        \n        .stat-value {\n            font-size: 2.5rem;\n            font-weight: 700;\n            color: var(--accent);\n            margin: 10px 0;\n        }\n        \n        .stat-label {\n            font-size: 1.1rem;\n            color: var(--dark);\n            font-weight: 500;\n        }\n        \n        .chart-container {\n            background: var(--card-bg);\n            border-radius: 15px;\n            padding: 30px;\n            margin-bottom: 40px;\n            box-shadow: 0 8px 20px rgba(242, 95, 92, 0.1);\n            border: 1px solid rgba(255, 140, 66, 0.2);\n        }\n        \n        .section-title {\n            display: flex;\n            align-items: center;\n            color: var(--dark);\n            margin-bottom: 25px;\n            font-size: 1.8rem;\n        }\n        \n        .section-title i {\n            margin-right: 12px;\n            color: var(--accent);\n        }\n        \n        .grid-2col {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));\n            gap: 30px;\n            margin-bottom: 40px;\n        }\n        \n        .topic-card {\n            background: var(--card-bg);\n            border-radius: 15px;\n            padding: 25px;\n            box-shadow: 0 8px 20px rgba(242, 95, 92, 0.1);\n            border: 1px solid rgba(255, 140, 66, 0.2);\n        }\n        \n        .topic-header {\n            display: flex;\n            align-items: center;\n            margin-bottom: 20px;\n        }\n        \n        .topic-icon {\n            width: 50px;\n            height: 50px;\n            background: var(--primary);\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            margin-right: 15px;\n            color: white;\n            font-size: 1.5rem;\n        }\n        \n        .topic-title {\n            font-size: 1.5rem;\n            color: var(--dark);\n            font-weight: 600;\n        }\n        \n        .topic-summary {\n            background: rgba(255, 181, 99, 0.2);\n            border-left: 4px solid var(--accent);\n            padding: 15px;\n            border-radius: 0 8px 8px 0;\n            margin-bottom: 20px;\n            font-size: 1.1rem;\n        }\n        \n        .dialogue-container {\n            max-height: 300px;\n            overflow-y: auto;\n            padding-right: 10px;\n        }\n        \n        .message-bubble {\n            padding: 12px 18px;\n            border-radius: 18px;\n            margin-bottom: 15px;\n            max-width: 85%;\n            position: relative;\n            animation: fadeIn 0.5s ease;\n        }\n        \n        @keyframes fadeIn {\n            from { opacity: 0; transform: translateY(10px); }\n            to { opacity: 1; transform: translateY(0); }\n        }\n        \n        .message-left {\n            background: rgba(255, 181, 99, 0.3);\n            border-top-left-radius: 5px;\n            margin-right: auto;\n        }\n        \n        .message-right {\n            background: rgba(255, 140, 66, 0.3);\n            border-top-right-radius: 5px;\n            margin-left: auto;\n        }\n        \n        .speaker-info {\n            font-weight: 600;\n            color: var(--accent);\n            margin-bottom: 5px;\n            font-size: 0.9rem;\n        }\n        \n        .dialogue-content {\n            font-size: 1.1rem;\n            line-height: 1.5;\n        }\n        \n        .word-cloud {\n            display: flex;\n            flex-wrap: wrap;\n            justify-content: center;\n            gap: 12px;\n            padding: 25px;\n            background: var(--card-bg);\n            border-radius: 15px;\n            margin-bottom: 40px;\n            box-shadow: 0 8px 20px rgba(242, 95, 92, 0.1);\n            border: 1px solid rgba(255, 140, 66, 0.2);\n        }\n        \n        .word {\n            padding: 8px 16px;\n            border-radius: 30px;\n            background: linear-gradient(135deg, var(--secondary) 0%, var(--primary) 100%);\n            color: white;\n            font-weight: 600;\n            transition: all 0.3s ease;\n            box-shadow: 0 4px 8px rgba(242, 95, 92, 0.2);\n        }\n        \n        .word:hover {\n            transform: scale(1.1);\n            box-shadow: 0 6px 12px rgba(242, 95, 92, 0.3);\n        }\n        \n        .word-1 { font-size: 1.8rem; }\n        .word-2 { font-size: 1.6rem; }\n        .word-3 { font-size: 1.4rem; }\n        .word-4 { font-size: 1.2rem; }\n        .word-5 { font-size: 1.1rem; }\n        \n        .concept-map {\n            background: var(--card-bg);\n            border-radius: 15px;\n            padding: 30px;\n            margin-bottom: 40px;\n            box-shadow: 0 8px 20px rgba(242, 95, 92, 0.1);\n            border: 1px solid rgba(255, 140, 66, 0.2);\n            min-height: 400px;\n        }\n        \n        footer {\n            text-align: center;\n            padding: 30px 0;\n            color: var(--dark);\n            font-size: 1.1rem;\n            border-top: 1px solid rgba(255, 140, 66, 0.3);\n            margin-top: 20px;\n        }\n        \n        @media (max-width: 768px) {\n            .grid-2col {\n                grid-template-columns: 1fr;\n            }\n            \n            h1 {\n                font-size: 2.2rem;\n            }\n            \n            .section-title {\n                font-size: 1.5rem;\n            }\n            \n            .chart-container {\n                padding: 20px;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header>\n            <h1>【1】AI产品蝗虫团</h1>\n            <p class=\"subtitle\">2025年6月21日 聊天数据分析报告</p>\n        </header>\n        \n        <div class=\"stats-grid\">\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">500</div>\n                <div class=\"stat-label\">消息总数</div>\n                <i class=\"fas fa-comments fa-2x\" style=\"color: var(--accent); margin-top: 15px;\"></i>\n            </div>\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">446</div>\n                <div class=\"stat-label\">有效文本消息</div>\n                <i class=\"fas fa-file-alt fa-2x\" style=\"color: var(--accent); margin-top: 15px;\"></i>\n            </div>\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">47</div>\n                <div class=\"stat-label\">活跃用户数</div>\n                <i class=\"fas fa-users fa-2x\" style=\"color: var(--accent); margin-top: 15px;\"></i>\n            </div>\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">18小时</div>\n                <div class=\"stat-label\">持续时长</div>\n                <i class=\"fas fa-clock fa-2x\" style=\"color: var(--accent); margin-top: 15px;\"></i>\n            </div>\n        </div>\n        \n        <div class=\"chart-container\">\n            <h2 class=\"section-title\"><i class=\"fas fa-chart-bar\"></i> 活跃用户分析</h2>\n            <canvas id=\"userChart\"></canvas>\n        </div>\n        \n        <div class=\"chart-container\">\n            <h2 class=\"section-title\"><i class=\"fas fa-clock\"></i> 消息时间分布</h2>\n            <canvas id=\"timeChart\"></canvas>\n        </div>\n        \n        <div class=\"concept-map\">\n            <h2 class=\"section-title\"><i class=\"fas fa-project-diagram\"></i> 核心概念关系图</h2>\n            <div class=\"mermaid\">\nflowchart LR\n    A[AI Agent] --> B(生产力提升)\n    A --> C(编程辅助)\n    D[大模型竞争] --> E(Kimi争议)\n    D --> F(Gemini发展)\n    G[AI创业] --> H(超级个体)\n    G --> I(团队协作)\n    J[AI浏览器] --> K(DIA vs 豆包)\n    J --> L(用户体验)\n    B --> M(开发效率)\n    C --> N(Gemini Code Assist)\n    E --> O(推广策略)\n    F --> P(视频分析)\n    H --> Q(一人公司)\n    I --> R(AI领导力)\n            </div>\n        </div>\n        \n        <div class=\"word-cloud\">\n            <div class=\"word word-1\">AI</div>\n            <div class=\"word word-2\">Gemini</div>\n            <div class=\"word word-1\">Kimi</div>\n            <div class=\"word word-3\">创业</div>\n            <div class=\"word word-2\">开发</div>\n            <div class=\"word word-4\">大模型</div>\n            <div class=\"word word-3\">生产力</div>\n            <div class=\"word word-5\">DIA</div>\n            <div class=\"word word-4\">编程</div>\n            <div class=\"word word-3\">Agent</div>\n            <div class=\"word word-2\">谷歌</div>\n            <div class=\"word word-5\">苹果</div>\n            <div class=\"word word-4\">个体户</div>\n            <div class=\"word word-3\">领导力</div>\n        </div>\n        \n        <div class=\"grid-2col\">\n            <div class=\"topic-card\">\n                <div class=\"topic-header\">\n                    <div class=\"topic-icon\"><i class=\"fas fa-robot\"></i></div>\n                    <h3 class=\"topic-title\">AI工具与生产力革命</h3>\n                </div>\n                <div class=\"topic-summary\">\n                    群内深入讨论了Gemini、Kimi等AI工具如何重塑工作流程，Gemini Code Assist等编程辅助工具大幅提升开发效率，引发关于AI如何改变工作范式的探讨。\n                </div>\n                <div class=\"dialogue-container\">\n                    <div class=\"message-bubble message-left\">\n                        <div class=\"speaker-info\">Ronin_Chang (08:02:44)</div>\n                        <div class=\"dialogue-content\">马斯克又在胖揍奥特曼了 [吃瓜]</div>\n                    </div>\n                    <div class=\"message-bubble message-right\">\n                        <div class=\"speaker-info\">神的孩子在跳舞 (10:16:46)</div>\n                        <div class=\"dialogue-content\">Gemini Code Assist 个人免费使用，支持VS Code和Jetbrains</div>\n                    </div>\n                    <div class=\"message-bubble message-left\">\n                        <div class=\"speaker-info\">Dale Xiao🐳 (09:49:49)</div>\n                        <div class=\"dialogue-content\">现在开发写代码都开多个AI，从结果里挑最好的用，这就是生产力提升的体感</div>\n                    </div>\n                    <div class=\"message-bubble message-right\">\n                        <div class=\"speaker-info\">年轮 (09:50:26)</div>\n                        <div class=\"dialogue-content\">这头大象甚至开始转身跳街舞，谷歌的更新速度惊人</div>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"topic-card\">\n                <div class=\"topic-header\">\n                    <div class=\"topic-icon\"><i class=\"fas fa-lightbulb\"></i></div>\n                    <h3 class=\"topic-title\">AI创业与未来工作</h3>\n                </div>\n                <div class=\"topic-summary\">\n                    关于AI时代创业模式的激烈讨论，聚焦超级个体与团队协作的平衡，探讨AI领导力如何重塑管理方式和工作形态。\n                </div>\n                <div class=\"dialogue-container\">\n                    <div class=\"message-bubble message-left\">\n                        <div class=\"speaker-info\">Eliot (17:39:30)</div>\n                        <div class=\"dialogue-content\">AI领导力比学会用AI更重要，领导要有系统思维和管理经验</div>\n                    </div>\n                    <div class=\"message-bubble message-right\">\n                        <div class=\"speaker-info\">修 (17:47:41)</div>\n                        <div class=\"dialogue-content\">个体户需要极致的效率，一人当N人用，AI是必备工具</div>\n                    </div>\n                    <div class=\"message-bubble message-left\">\n                        <div class=\"speaker-info\">秦岭 (17:55:26)</div>\n                        <div class=\"dialogue-content\">只有内驱力、执行力最强的人，才做得来一人企业</div>\n                    </div>\n                    <div class=\"message-bubble message-right\">\n                        <div class=\"speaker-info\">Eliot (17:47:13)</div>\n                        <div class=\"dialogue-content\">AI会淘汰掉不合格的个体户，不具备AI能力的将被淘汰</div>\n                    </div>\n                </div>\n            </div>\n        </div>\n        \n        <footer>\n            <p>生成时间：2025年6月22日 | 数据分析报告</p>\n            <p style=\"margin-top: 10px; opacity: 0.8;\">暖色系设计 · 响应式布局 · 数据可视化</p>\n        </footer>\n    </div>\n\n    <script>\n        // 初始化Mermaid\n        mermaid.initialize({\n            startOnLoad: true,\n            theme: 'base',\n            themeVariables: {\n                primaryColor: '#FFE8D6',\n                nodeBorder: '#FF8C42',\n                lineColor: '#F25F5C',\n                textColor: '#5C4033'\n            }\n        });\n        \n        // 用户消息统计图表\n        const userCtx = document.getElementById('userChart').getContext('2d');\n        new Chart(userCtx, {\n            type: 'bar',\n            data: {\n                labels: ['神的孩子在跳舞', 'Ronin_Chang', '修', '风物长宜放眼量', 'Eliot'],\n                datasets: [{\n                    label: '发言条数',\n                    data: [77, 53, 40, 33, 22],\n                    backgroundColor: [\n                        'rgba(255, 140, 66, 0.7)',\n                        'rgba(255, 181, 99, 0.7)',\n                        'rgba(242, 95, 92, 0.7)',\n                        'rgba(255, 195, 135, 0.7)',\n                        'rgba(255, 160, 122, 0.7)'\n                    ],\n                    borderColor: [\n                        'rgba(255, 140, 66, 1)',\n                        'rgba(255, 181, 99, 1)',\n                        'rgba(242, 95, 92, 1)',\n                        'rgba(255, 195, 135, 1)',\n                        'rgba(255, 160, 122, 1)'\n                    ],\n                    borderWidth: 2,\n                    borderRadius: 8\n                }]\n            },\n            options: {\n                responsive: true,\n                plugins: {\n                    legend: {\n                        display: false\n                    },\n                    tooltip: {\n                        backgroundColor: 'rgba(92, 64, 51, 0.9)',\n                        titleFont: { size: 16 },\n                        bodyFont: { size: 14 }\n                    }\n                },\n                scales: {\n                    y: {\n                        beginAtZero: true,\n                        grid: { color: 'rgba(92, 64, 51, 0.1)' },\n                        ticks: { color: '#5C4033', font: { size: 14 } }\n                    },\n                    x: {\n                        grid: { display: false },\n                        ticks: { color: '#5C4033', font: { size: 14 } }\n                    }\n                }\n            }\n        });\n        \n        // 时间分布图表\n        const timeCtx = document.getElementById('timeChart').getContext('2d');\n        new Chart(timeCtx, {\n            type: 'line',\n            data: {\n                labels: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00'],\n                datasets: [{\n                    label: '每小时消息量',\n                    data: [18, 12, 5, 22, 68, 57, 32, 28, 31, 45, 18, 9],\n                    fill: true,\n                    backgroundColor: 'rgba(255, 140, 66, 0.2)',\n                    borderColor: 'rgba(242, 95, 92, 1)',\n                    borderWidth: 3,\n                    pointBackgroundColor: '#FF8C42',\n                    pointRadius: 6,\n                    tension: 0.2\n                }]\n            },\n            options: {\n                responsive: true,\n                plugins: {\n                    tooltip: {\n                        backgroundColor: 'rgba(92, 64, 51, 0.9)',\n                        titleFont: { size: 16 },\n                        bodyFont: { size: 14 }\n                    }\n                },\n                scales: {\n                    y: {\n                        beginAtZero: true,\n                        grid: { color: 'rgba(92, 64, 51, 0.1)' },\n                        ticks: { color: '#5C4033', font: { size: 14 } }\n                    },\n                    x: {\n                        grid: { color: 'rgba(92, 64, 51, 0.1)' },\n                        ticks: { color: '#5C4033', font: { size: 14 } }\n                    }\n                }\n            }\n        });\n        \n        // 滚动动画\n        const observer = new IntersectionObserver((entries) => {\n            entries.forEach(entry => {\n                if (entry.isIntersecting) {\n                    entry.target.style.animation = `fadeIn 0.8s ease forwards`;\n                }\n            });\n        }, { threshold: 0.1 });\n        \n        document.querySelectorAll('.message-bubble').forEach(bubble => {\n            bubble.style.opacity = '0';\n            observer.observe(bubble);\n        });\n    </script>\n</body>\n</html>\n```", "savedAt": "2025-06-22T02:06:58.846Z"}