{"title": "[定时] 科学学习分析 - 智能体1群|一支烟花社区", "groupName": "智能体1群|一支烟花社区", "analysisType": "science", "timeRange": "2025-06-17~2025-06-17", "messageCount": 3, "isScheduled": true, "content": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>智能体1群|一支烟花社区 - 聊天分析报告</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js\"></script>\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n    <style>\n        :root {\n            --primary: #FF9F43;\n            --secondary: #FECA57;\n            --light: #FFF5E6;\n            --dark: #5C4033;\n            --accent: #FF6B6B;\n        }\n        \n        body {\n            font-family: -apple-system, BlinkMacSystemFont, \"<PERSON><PERSON><PERSON> UI\", <PERSON><PERSON>, \"Helvetica Neue\", <PERSON><PERSON>, \"Noto Sans SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"WenQuanYi Micro Hei\", sans-serif;\n            background-color: var(--light);\n            color: var(--dark);\n            line-height: 1.6;\n            margin: 0;\n            padding: 0;\n        }\n        \n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n            padding: 20px;\n        }\n        \n        header {\n            background: linear-gradient(135deg, var(--primary), var(--secondary));\n            color: white;\n            padding: 30px 20px;\n            border-radius: 10px;\n            margin-bottom: 30px;\n            box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n            text-align: center;\n        }\n        \n        h1 {\n            font-size: 2.2rem;\n            margin: 0;\n            font-weight: 700;\n        }\n        \n        h2 {\n            color: var(--primary);\n            font-size: 1.8rem;\n            margin-top: 40px;\n            border-bottom: 2px solid var(--secondary);\n            padding-bottom: 10px;\n        }\n        \n        h3 {\n            color: var(--dark);\n            font-size: 1.4rem;\n            margin-top: 25px;\n        }\n        \n        .card {\n            background-color: white;\n            border-radius: 10px;\n            padding: 20px;\n            margin-bottom: 20px;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.05);\n            transition: transform 0.3s, box-shadow 0.3s;\n        }\n        \n        .card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 8px 20px rgba(0,0,0,0.1);\n        }\n        \n        .keyword-tag {\n            display: inline-block;\n            background-color: var(--secondary);\n            color: var(--dark);\n            padding: 5px 15px;\n            border-radius: 20px;\n            margin: 5px;\n            font-weight: 500;\n            box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n        }\n        \n        .chart-container {\n            position: relative;\n            height: 300px;\n            margin: 20px 0;\n        }\n        \n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 20px;\n            margin: 30px 0;\n        }\n        \n        .stat-box {\n            background-color: white;\n            border-radius: 10px;\n            padding: 20px;\n            text-align: center;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.05);\n        }\n        \n        .stat-number {\n            font-size: 2.5rem;\n            font-weight: 700;\n            color: var(--primary);\n            margin: 10px 0;\n        }\n        \n        .stat-label {\n            color: var(--dark);\n            font-size: 1rem;\n        }\n        \n        .empty-state {\n            text-align: center;\n            padding: 40px;\n            color: #999;\n            background-color: rgba(255,255,255,0.7);\n            border-radius: 10px;\n            margin: 20px 0;\n        }\n        \n        .empty-state i {\n            font-size: 3rem;\n            margin-bottom: 20px;\n            color: var(--secondary);\n        }\n        \n        footer {\n            text-align: center;\n            margin-top: 50px;\n            padding: 20px;\n            color: #999;\n            font-size: 0.9rem;\n        }\n        \n        @media (max-width: 768px) {\n            h1 {\n                font-size: 1.8rem;\n            }\n            \n            .stats-grid {\n                grid-template-columns: 1fr;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header>\n            <h1>智能体1群|一支烟花社区</h1>\n            <p>2025年6月17日 聊天分析报告</p>\n        </header>\n        \n        <div class=\"stats-grid\">\n            <div class=\"stat-box\">\n                <div class=\"stat-number\">3</div>\n                <div class=\"stat-label\">消息总数</div>\n            </div>\n            <div class=\"stat-box\">\n                <div class=\"stat-number\">0</div>\n                <div class=\"stat-label\">有效文本消息</div>\n            </div>\n            <div class=\"stat-box\">\n                <div class=\"stat-number\">0</div>\n                <div class=\"stat-label\">活跃用户数</div>\n            </div>\n            <div class=\"stat-box\">\n                <div class=\"stat-number\">2</div>\n                <div class=\"stat-label\">时间跨度(小时)</div>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>时间分布</h2>\n            <div class=\"chart-container\">\n                <canvas id=\"timeChart\"></canvas>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>关键词分析</h2>\n            <div class=\"empty-state\">\n                <i class=\"fas fa-search\"></i>\n                <p>本次聊天中没有提取到有效的关键词</p>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>核心概念关系图</h2>\n            <div class=\"empty-state\">\n                <i class=\"fas fa-project-diagram\"></i>\n                <p>本次聊天中没有足够内容生成概念关系图</p>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>聊天内容分析</h2>\n            <div class=\"empty-state\">\n                <i class=\"fas fa-comments\"></i>\n                <p>本次聊天中没有有效的文本消息内容</p>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>时间范围</h2>\n            <p>2025-06-17 06:21:52 至 2025-06-17 08:06:54</p>\n        </div>\n    </div>\n    \n    <footer>\n        <p>报告生成时间: <span id=\"reportTime\"></span></p>\n        <p>© 2025 聊天分析报告 | 使用Chart.js和Mermaid.js生成</p>\n    </footer>\n    \n    <script>\n        // 设置报告生成时间\n        document.getElementById('reportTime').textContent = new Date().toLocaleString();\n        \n        // 初始化时间分布图表\n        const timeCtx = document.getElementById('timeChart').getContext('2d');\n        const timeChart = new Chart(timeCtx, {\n            type: 'bar',\n            data: {\n                labels: ['06:00-07:00', '07:00-08:00', '08:00-09:00'],\n                datasets: [{\n                    label: '消息数量',\n                    data: [1, 2, 0],\n                    backgroundColor: [\n                        'rgba(255, 159, 67, 0.7)',\n                        'rgba(254, 202, 87, 0.7)',\n                        'rgba(255, 107, 107, 0.7)'\n                    ],\n                    borderColor: [\n                        'rgba(255, 159, 67, 1)',\n                        'rgba(254, 202, 87, 1)',\n                        'rgba(255, 107, 107, 1)'\n                    ],\n                    borderWidth: 1\n                }]\n            },\n            options: {\n                responsive: true,\n                plugins: {\n                    legend: {\n                        position: 'top',\n                    },\n                    title: {\n                        display: true,\n                        text: '每小时消息分布'\n                    }\n                },\n                scales: {\n                    y: {\n                        beginAtZero: true,\n                        ticks: {\n                            stepSize: 1\n                        }\n                    }\n                }\n            }\n        });\n        \n        // 初始化Mermaid\n        mermaid.initialize({\n            startOnLoad: true,\n            theme: 'default',\n            themeVariables: {\n                primaryColor: '#FFE8D6',\n                nodeBorder: '#FF9F43',\n                lineColor: '#FECA57',\n                textColor: '#5C4033'\n            }\n        });\n    </script>\n</body>\n</html>", "savedAt": "2025-06-17T16:37:02.474Z"}