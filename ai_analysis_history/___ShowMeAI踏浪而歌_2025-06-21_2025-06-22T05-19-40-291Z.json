{"groupName": "🌊 ShowMeAI踏浪而歌", "analysisType": "custom", "timeRange": "2025-06-21", "messageCount": 173, "timestamp": "2025-06-22T05:19:40.291Z", "title": "🌊 ShowMeAI踏浪而歌 - 自定义分析", "content": "```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>🌊 ShowMeAI踏浪而歌 - 2025-06-21 聊天精华报告</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n    <style>\n        :root {\n            --bg-main: #FFFBF5;\n            --bg-card: #FFFFFF;\n            --bg-card-alt: #FEF7EC;\n            --text-primary: #3D352E;\n            --text-secondary: #7A6A5C;\n            --accent-color: #E58D42;\n            --accent-hover: #D17C32;\n            --border-color: #F3E9DD;\n            --shadow-color: rgba(140, 91, 47, 0.1);\n            --keyword-bg: #FAEBDD;\n            --keyword-text: #8C5B2F;\n            --quote-bg: #FDF2E6;\n        }\n\n        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');\n\n        body {\n            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\n            background-color: var(--bg-main);\n            color: var(--text-primary);\n            line-height: 1.8;\n            margin: 0;\n            padding: 20px;\n            font-size: 16px;\n        }\n\n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n        }\n\n        header {\n            text-align: center;\n            margin-bottom: 40px;\n            padding: 20px;\n            background-color: var(--bg-card);\n            border-radius: 20px;\n            border: 1px solid var(--border-color);\n            box-shadow: 0 8px 20px var(--shadow-color);\n        }\n\n        header h1 {\n            font-size: 36px;\n            color: var(--accent-color);\n            margin: 0 0 10px 0;\n            font-weight: 700;\n        }\n\n        header p {\n            font-size: 16px;\n            color: var(--text-secondary);\n            margin: 0;\n        }\n        \n        .bento-grid {\n            display: grid;\n            grid-template-columns: repeat(12, 1fr);\n            gap: 20px;\n        }\n\n        .bento-card {\n            background-color: var(--bg-card);\n            padding: 25px 30px;\n            border-radius: 20px;\n            border: 1px solid var(--border-color);\n            box-shadow: 0 4px 15px var(--shadow-color);\n            transition: transform 0.3s ease, box-shadow 0.3s ease;\n            overflow: hidden;\n        }\n\n        .bento-card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 10px 25px rgba(140, 91, 47, 0.15);\n        }\n\n        .bento-card h2 {\n            font-size: 26px;\n            color: var(--text-primary);\n            margin-top: 0;\n            margin-bottom: 20px;\n            border-bottom: 2px solid var(--accent-color);\n            padding-bottom: 10px;\n            display: inline-block;\n        }\n\n        .bento-card h3 {\n            font-size: 20px;\n            color: var(--accent-color);\n            margin-top: 25px;\n            margin-bottom: 15px;\n            font-weight: 500;\n        }\n        \n        /* Grid item spans */\n        .span-12 { grid-column: span 12; }\n        .span-8 { grid-column: span 8; }\n        .span-7 { grid-column: span 7; }\n        .span-6 { grid-column: span 6; }\n        .span-5 { grid-column: span 5; }\n        .span-4 { grid-column: span 4; }\n\n        /* Specific card styles */\n        .stats-card {\n            display: flex;\n            justify-content: space-around;\n            align-items: center;\n            text-align: center;\n            background: linear-gradient(135deg, var(--bg-card-alt), var(--bg-card));\n        }\n\n        .stat-item {\n            flex: 1;\n        }\n\n        .stat-item .value {\n            font-size: 32px;\n            font-weight: 700;\n            color: var(--accent-color);\n        }\n\n        .stat-item .label {\n            font-size: 14px;\n            color: var(--text-secondary);\n        }\n\n        .keywords-container {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 12px;\n        }\n\n        .keyword-tag {\n            background-color: var(--keyword-bg);\n            color: var(--keyword-text);\n            padding: 8px 16px;\n            border-radius: 30px;\n            font-size: 14px;\n            font-weight: 500;\n            transition: background-color 0.3s;\n        }\n        .keyword-tag:hover {\n            background-color: #F5DCC4;\n        }\n\n        .mermaid-container {\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            min-height: 300px;\n        }\n        \n        .mermaid {\n            width: 100%;\n        }\n\n        .topic-description {\n            color: var(--text-secondary);\n            font-size: 15px;\n            margin-bottom: 20px;\n            padding-left: 15px;\n            border-left: 3px solid var(--border-color);\n        }\n\n        .dialogue-container {\n            background-color: #FCF8F3;\n            border: 1px solid var(--border-color);\n            border-radius: 10px;\n            padding: 20px;\n            margin-top: 20px;\n        }\n        \n        .message-bubble {\n            margin-bottom: 12px;\n            max-width: 90%;\n            display: flex;\n            flex-direction: column;\n        }\n\n        .message-bubble .author {\n            font-weight: 700;\n            color: var(--accent-color);\n            font-size: 14px;\n            margin-bottom: 4px;\n        }\n        \n        .message-bubble .content {\n            background-color: #fff;\n            padding: 10px 15px;\n            border-radius: 12px;\n            border: 1px solid #eee;\n            word-wrap: break-word;\n            display: inline-block;\n        }\n        \n        .quotes-grid {\n            display: grid;\n            grid-template-columns: repeat(2, 1fr);\n            gap: 20px;\n        }\n\n        .quote-card {\n            background-color: var(--quote-bg);\n            padding: 20px;\n            border-radius: 15px;\n            border-left: 5px solid var(--accent-color);\n        }\n\n        .quote-text {\n            font-size: 17px;\n            font-style: italic;\n            color: var(--text-primary);\n            margin-bottom: 10px;\n        }\n        .quote-author {\n            text-align: right;\n            font-weight: 500;\n            color: var(--text-secondary);\n        }\n        .interpretation-area {\n            margin-top: 15px;\n            font-size: 14px;\n            color: #8C5B2F;\n            border-top: 1px dashed var(--border-color);\n            padding-top: 10px;\n        }\n        .interpretation-area strong {\n            color: #7a5533;\n        }\n\n        .resource-list ul {\n            list-style: none;\n            padding: 0;\n        }\n\n        .resource-list li {\n            margin-bottom: 15px;\n            padding: 10px;\n            border-radius: 8px;\n            transition: background-color 0.3s;\n        }\n        .resource-list li:hover {\n            background-color: #fcf8f3;\n        }\n\n        .resource-list a {\n            color: var(--accent-color);\n            text-decoration: none;\n            font-weight: 500;\n        }\n\n        .resource-list a:hover {\n            text-decoration: underline;\n        }\n        .resource-list p {\n            margin: 5px 0 0 0;\n            color: var(--text-secondary);\n            font-size: 14px;\n        }\n\n        footer {\n            text-align: center;\n            margin-top: 40px;\n            padding: 20px;\n            font-size: 14px;\n            color: var(--text-secondary);\n        }\n\n        @media (max-width: 992px) {\n            .bento-card {\n                grid-column: span 12 !important;\n            }\n            .stats-card {\n                flex-direction: column;\n                gap: 20px;\n            }\n        }\n        \n        @media (max-width: 768px) {\n            body { padding: 10px; }\n            header h1 { font-size: 28px; }\n            .bento-card { padding: 20px; }\n            .quotes-grid { grid-template-columns: 1fr; }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header>\n            <h1>🌊 ShowMeAI踏浪而歌 - 聊天精华报告</h1>\n            <p>日期：2025年06月21日</p>\n        </header>\n\n        <main class=\"bento-grid\">\n            <div class=\"bento-card stats-card span-12\">\n                <div class=\"stat-item\">\n                    <div class=\"value\">173</div>\n                    <div class=\"label\"><i class=\"fas fa-comments\"></i> 消息总数</div>\n                </div>\n                <div class=\"stat-item\">\n                    <div class=\"value\">142</div>\n                    <div class=\"label\"><i class=\"fas fa-file-alt\"></i> 有效文本</div>\n                </div>\n                <div class=\"stat-item\">\n                    <div class=\"value\">37</div>\n                    <div class=\"label\"><i class=\"fas fa-users\"></i> 活跃用户</div>\n                </div>\n            </div>\n            \n            <div class=\"bento-card span-5\">\n                <h2><i class=\"fas fa-tags\"></i> 本日核心议题</h2>\n                <div class=\"keywords-container\">\n                    <span class=\"keyword-tag\">AI编程助手</span>\n                    <span class=\"keyword-tag\">Cursor</span>\n                    <span class=\"keyword-tag\">Augment Code</span>\n                    <span class=\"keyword-tag\">Perplexity (PPLX)</span>\n                    <span class=\"keyword-tag\">Google Code Assistant</span>\n                    <span class=\"keyword-tag\">AIGC</span>\n                    <span class=\"keyword-tag\">声音克隆</span>\n                    <span class=\"keyword-tag\">WSL开发</span>\n                    <span class=\"keyword-tag\">AI产品策略</span>\n                </div>\n            </div>\n\n            <div class=\"bento-card span-7\">\n                <h2><i class=\"fas fa-users-cog\"></i> 活跃用户Top 5</h2>\n                <canvas id=\"topSpeakersChart\"></canvas>\n            </div>\n\n            <div class=\"bento-card span-12\">\n                <h2><i class=\"fas fa-project-diagram\"></i> 核心概念关系图</h2>\n                <div class=\"mermaid-container\">\n                    <pre class=\"mermaid\">\ngraph LR;\n    subgraph A[AI编程助手]\n        direction LR\n        Cursor(\"Cursor\")\n        Augment(\"Augment Code\")\n        GCA(\"Google Code Assistant\")\n    end\n\n    subgraph B[AI应用与策略]\n        direction LR\n        PPLX(\"Perplexity (PPLX)\")\n        AIGC(\"AIGC\")\n    end\n\n    subgraph C[技术栈与工具]\n        direction LR\n        WSL(\"WSL 开发\")\n        VoiceClone(\"声音克隆\")\n    end\n\n    UserExperience(\"用户体验 & 成本\")\n\n    Cursor -- \"对比\" --> Augment;\n    GCA -- \"被吐槽\" --> UserExperience;\n    Augment -- \"价格高昂\" --> UserExperience;\n    Cursor -- \"与WSL结合\" --> WSL;\n    PPLX -- \"讨论收购与商业模式\" --> B;\n    AIGC -- \"包含\" --> VoiceClone;\n\n    classDef default fill:#fff,stroke:#D17C32,stroke-width:2px,color:#3D352E;\n    classDef subgraph fill:#FFFBF5,stroke:#F3E9DD,color:#7A6A5C;\n    class A,B,C subgraph;\n                    </pre>\n                </div>\n            </div>\n\n            <div class=\"bento-card span-12\">\n                <h2><i class=\"fas fa-fire\"></i> 精华话题聚焦</h2>\n\n                <h3><i class=\"fas fa-laptop-code\"></i> 话题一：AI编程助手大比拼：Cursor, Augment与Google的爱恨情仇</h3>\n                <p class=\"topic-description\">\n                    本日最热门的讨论聚焦于AI编程助手。由 <strong>一歲抬頭、w、雄雄的小课堂、卡夫卡</strong> 等多位开发者主导，对市面主流工具进行了深度剖析。大家普遍认为 <strong>Cursor</strong> 虽然有一定“笨拙感”，如“屡教不改”、“多改文件”等问题，但其与WSL的良好集成和git-like的修改审查机制是其优点。而 <strong>Augment Code</strong> 被誉为“当前最强”，代码质量和智能性受到高度评价，但其高昂的定价（50美元300点）令许多人望而却步，形成“强但贵”的典型代表。相比之下，<strong>Google Code Assistant</strong> 则遭到了普遍吐槽，参与者认为尽管Google拥有强大的Gemini模型，但其工程实现非常糟糕，产品体验远不及竞品，被评价为“负分滚粗”。\n                </p>\n                <h4><i class=\"fas fa-comments\"></i> 重要对话节选</h4>\n                <div class=\"dialogue-container\">\n                    <div class=\"message-bubble\"><div class=\"author\">一歲抬頭</div><div class=\"content\">请问现在还有比Claude code，Augment能打的AI编程工具么</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">社恐患者杨老师</div><div class=\"content\">cursor</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">卡夫卡</div><div class=\"content\">要不是价格贵的离谱，我就以Augment Code为主力开发工具了[捂脸]</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">一歲抬頭</div><div class=\"content\">要用就用当前最强，时间就是金钱[旺柴]</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">w</div><div class=\"content\">cursor是感觉笨笨的</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">w</div><div class=\"content\">笨的表现：<br>1.反复说还是没听懂，屡教不改<br>2.多改了不需要改的文件<br>3.jupyter notebook支持不太好，给一次指示，就要新建一个单元格，并且很固执地在固定的位置加</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">雄雄的小课堂</div><div class=\"content\">刚看到，谢谢回复，还想问一下，是用的windos系统吗，wsl的方式结合cursor用的嘛</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">Elliot Bai</div><div class=\"content\">是的，wsl，cursor 也是在 wsl</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">雄雄的小课堂</div><div class=\"content\">其实rule是最关键的，不然cursor会乱写</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">wyz</div><div class=\"content\">我一般让他 做 改变前 先 形式化成 数学 然后 再 询问我的 意见。现在这些 ai 特别是 推理模型 特别容易自我兴奋 然后就 不知所以了，我试过 augment 也 容易加 冗余的不必要操作，然后把关键步骤 用 naive 的 方式 绕过去 就像是 应付一样。cursor 好在 有 git 修改一样的 标识，可以自己慢慢 对着 接受或者 取消。</div></div>\n                </div>\n\n                <h3><i class=\"fas fa-search-dollar\"></i> 话题二：Perplexity (PPLX) 的资本迷局与产品价值</h3>\n                <p class=\"topic-description\">\n                    由 <strong>海平面</strong> 抛出“Perplexity营销结束，账号开始收回”的消息引发了关于PPLX的讨论。<strong>南乔@ShowMeAI</strong> 提供了深度的行业观察，指出PPLX远非一个简单的C端搜索应用。他强调了其优秀的移动端交互、通过收购整合企业数据服务的能力，以及拥有自研模型（如Sonar系列）。这解释了为何有传闻称苹果考虑收购PPLX以补齐AI短板。群友们认为PPLX的策略非常“聪明”，在AI的“春秋时代”结束，“战国时代”开始之际，通过闪转腾挪，在关键时间窗口攫取了最大利益。这波讨论揭示了PPLX在产品、技术和商业策略上的多层次布局。\n                </p>\n                <h4><i class=\"fas fa-comments\"></i> 重要对话节选</h4>\n                <div class=\"dialogue-container\">\n                    <div class=\"message-bubble\"><div class=\"author\">海平面</div><div class=\"content\">perplexity营销结束了，账号开始收回了</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">社恐患者杨老师</div><div class=\"content\">苹果考虑收购初创公司 Perplexity，以补齐 AI「短板」</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">社恐患者杨老师</div><div class=\"content\">不过我好奇的是，收购Perplexity能补齐啥短板啊</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">南乔@ShowMeAI</div><div class=\"content\">pplx 并不只是 C 端搜索应用</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">南乔@ShowMeAI</div><div class=\"content\">它的移动端应用，跟手机交互挺不错的。B端收购了一家公司后，也能连企业自家数据了。</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">南乔@ShowMeAI</div><div class=\"content\">而且还有自己的模型。</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">南乔@ShowMeAI</div><div class=\"content\">pplx 是真的很聪明。闪转腾挪，时间窗口里攫取最大利益。</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">南乔@ShowMeAI</div><div class=\"content\">sonar</div></div>\n                    <div class=\"message-bubble\"><div class=\"author\">南乔@ShowMeAI</div><div class=\"content\">春秋结束，战国开始</div></div>\n                </div>\n            </div>\n\n            <div class=\"bento-card span-12\">\n                <h2><i class=\"fas fa-star\"></i> 群友金句闪耀</h2>\n                <div class=\"quotes-grid\">\n                    <div class=\"quote-card\">\n                        <p class=\"quote-text\">\"春秋结束，战国开始\"</p>\n                        <p class=\"quote-author\">- 南乔@ShowMeAI</p>\n                        <div class=\"interpretation-area\">\n                            <strong><i class=\"fas fa-lightbulb\"></i> AI解读：</strong> 这句话以历史作比，精准地描绘了当前AI行业的竞争格局。早期探索和机会遍地的“春秋时代”已经过去，现在进入了巨头林立、竞争激烈、合纵连横的“战国时代”。它预示着未来的AI竞争将更加残酷和体系化，小公司的生存空间被挤压，行业将迎来大规模的整合与洗牌。\n                        </div>\n                    </div>\n                    <div class=\"quote-card\">\n                        <p class=\"quote-text\">\"要用就用当前最强，时间就是金钱[旺柴]\"</p>\n                        <p class=\"quote-author\">- 一歲抬頭</p>\n                         <div class=\"interpretation-area\">\n                            <strong><i class=\"fas fa-lightbulb\"></i> AI解读：</strong> 这句话体现了极致的效率至上和价值导向思维。在技术快速迭代的今天，尤其对于开发者而言，生产力工具带来的时间节省和质量提升远超其本身的金钱成本。它倡导了一种投资未来的理念：为最顶尖的工具付费，就是为自己的时间和产出付费，是一种高回报的投资。\n                        </div>\n                    </div>\n                    <div class=\"quote-card\">\n                        <p class=\"quote-text\">\"现在这些 ai 特别是 推理模型 特别容易自我兴奋 然后就 不知所以了...\"</p>\n                        <p class=\"quote-author\">- wyz</p>\n                         <div class=\"interpretation-area\">\n                            <strong><i class=\"fas fa-lightbulb\"></i> AI解读：</strong> 这句生动的描述揭示了当前AI模型的一个核心缺陷：缺乏稳定的、可控的逻辑推理能力，有时会陷入“幻觉”或执行偏离指令的冗余操作。发言者提出的“先形式化成数学再询问”的解决方案，是一种深刻的AI交互思想，强调了在复杂任务中引入形式化验证和人机协同的重要性，以约束AI的“自由发挥”。\n                        </div>\n                    </div>\n                     <div class=\"quote-card\">\n                        <p class=\"quote-text\">\"结果一个月左右就成过时信息了[裂开]\"</p>\n                        <p class=\"quote-author\">- 宣</p>\n                         <div class=\"interpretation-area\">\n                            <strong><i class=\"fas fa-lightbulb\"></i> AI解读：</strong> 这句感叹道出了所有AI从业者和学习者共同的焦虑和无奈。它形象地说明了AI领域知识更新的速度之快，传统的学习方式（如看书）已难以跟上技术发展的步伐。这句话背后是对信息时效性的高度敏感，以及在“信息洪流”中保持前沿认知的巨大挑战。\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <div class=\"bento-card span-12\">\n                <h2><i class=\"fas fa-tools\"></i> 提及产品与资源</h2>\n                <div class=\"resource-list\">\n                    <ul>\n                        <li>\n                            <strong>Dify.ai</strong>\n                            <p>一个开源的LLM应用开发平台，旨在让开发者和非开发者能快速构建和运营AI应用。</p>\n                            <a href=\"https://docs.dify.ai/zh-hans/getting-started/install-self-hosted/readme\" target=\"_blank\"><i class=\"fas fa-link\"></i> 官方文档链接</a>\n                        </li>\n                         <li>\n                            <strong>Google Code Assistant</strong>\n                            <p>Google推出的集成在IDE中的AI编程辅助工具，基于其强大的Gemini模型。</p>\n                        </li>\n                        <li>\n                            <strong>Augment Code</strong>\n                            <p>一款主打高质量代码生成的AI编程工具，以其强大的性能和高昂的价格著称。</p>\n                        </li>\n                        <li>\n                            <strong>Cursor</strong>\n                            <p>一款流行的AI原生代码编辑器，深度集成了聊天、代码生成和编辑等功能。</p>\n                        </li>\n                        <li>\n                            <strong>PosterCraft</strong>\n                            <p>一个用于生成高质量美学海报的统一框架，特点是精准的文本渲染和风格协调。</p>\n                            <a href=\"https://github.com/ephemeral182/PosterCraft\" target=\"_blank\"><i class=\"fas fa-link\"></i> GitHub 仓库</a>\n                        </li>\n                         <li>\n                            <strong>Tencent Copilot IDE</strong>\n                            <p>腾讯推出的集成AI能力的集成开发环境，旨在提升开发者编码效率。</p>\n                            <a href=\"https://copilot.tencent.com/ide/\" target=\"_blank\"><i class=\"fas fa-link\"></i> 官方网站</a>\n                        </li>\n                        <li>\n                            <strong>AI Powered Knowledge Graph Generator</strong>\n                            <p>一个利用AI技术自动从文本生成知识图谱的工具项目。</p>\n                            <a href=\"https://github.com/robert-mcdermott/ai-knowledge-graph\" target=\"_blank\"><i class=\"fas fa-link\"></i> GitHub 仓库</a>\n                        </li>\n                    </ul>\n                </div>\n            </div>\n        </main>\n\n        <footer>\n            <p>由 AI 数据分析师和前端工程师联合生成</p>\n            <p>&copy; 2025 ShowMeAI. All Rights Reserved.</p>\n        </footer>\n    </div>\n    \n    <script type=\"module\">\n        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';\n\n        mermaid.initialize({ \n            startOnLoad: true,\n            theme: 'base',\n            themeVariables: {\n                background: '#FFFBF5',\n                primaryColor: '#FEF7EC',\n                primaryTextColor: '#3D352E',\n                primaryBorderColor: '#E58D42',\n                lineColor: '#D17C32',\n                secondaryColor: '#FAEBDD',\n                tertiaryColor: '#fff'\n            }\n        });\n\n        document.addEventListener('DOMContentLoaded', () => {\n            const ctx = document.getElementById('topSpeakersChart');\n            new Chart(ctx, {\n                type: 'bar',\n                data: {\n                    labels: ['那味X ᯅ', '雄雄的小课堂', '一歲抬頭', '南乔@ShowMeAI', '宣'],\n                    datasets: [{\n                        label: '消息数量',\n                        data: [16, 10, 10, 9, 8],\n                        backgroundColor: [\n                            'rgba(229, 141, 66, 0.7)',\n                            'rgba(229, 141, 66, 0.6)',\n                            'rgba(229, 141, 66, 0.5)',\n                            'rgba(229, 141, 66, 0.4)',\n                            'rgba(229, 141, 66, 0.3)'\n                        ],\n                        borderColor: [\n                            'rgba(229, 141, 66, 1)'\n                        ],\n                        borderWidth: 1\n                    }]\n                },\n                options: {\n                    indexAxis: 'y',\n                    responsive: true,\n                    plugins: {\n                        legend: {\n                            display: false\n                        },\n                        tooltip: {\n                            backgroundColor: 'rgba(61, 53, 46, 0.9)',\n                            titleFont: {\n                                size: 14,\n                                weight: 'bold',\n                            },\n                            bodyFont: {\n                                size: 12,\n                            },\n                            padding: 10,\n                            cornerRadius: 5\n                        }\n                    },\n                    scales: {\n                        x: {\n                            beginAtZero: true,\n                            grid: {\n                                color: '#F3E9DD'\n                            },\n                            ticks: {\n                                color: '#7A6A5C'\n                            }\n                        },\n                        y: {\n                            grid: {\n                                display: false\n                            },\n                            ticks: {\n                                color: '#3D352E',\n                                font: {\n                                    size: 14,\n                                }\n                            }\n                        }\n                    }\n                }\n            });\n        });\n    </script>\n</body>\n</html>\n```", "savedAt": "2025-06-22T05:19:40.291Z"}