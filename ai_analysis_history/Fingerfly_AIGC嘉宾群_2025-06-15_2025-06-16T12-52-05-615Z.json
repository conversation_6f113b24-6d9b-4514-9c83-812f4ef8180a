{"groupName": "Fingerfly AIGC嘉宾群", "analysisType": "custom", "timeRange": "2025-06-15", "messageCount": 18, "timestamp": "2025-06-16T12:52:05.615Z", "title": "Fingerfly AIGC嘉宾群 - 自定义分析", "content": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Fingerfly AIGC嘉宾群 - 2025年06月15日 聊天精华报告</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js\"></script>\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n    <style>\n        :root {\n            --primary-color: #FF7E5F;\n            --secondary-color: #FFB347;\n            --accent-color: #FFD166;\n            --light-bg: #FFF5E6;\n            --dark-text: #5C4033;\n            --medium-text: #8B4513;\n            --light-text: #A0522D;\n        }\n        \n        body {\n            font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", <PERSON><PERSON>, \"Helvetica Neue\", <PERSON><PERSON>, \"Noto Sans SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"WenQuanYi Micro Hei\", sans-serif;\n            background-color: var(--light-bg);\n            color: var(--dark-text);\n            line-height: 1.6;\n            padding: 0;\n            margin: 0;\n        }\n        \n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n            padding: 20px;\n        }\n        \n        header {\n            text-align: center;\n            padding: 30px 0;\n            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n            color: white;\n            border-radius: 15px;\n            margin-bottom: 30px;\n            box-shadow: 0 10px 20px rgba(0,0,0,0.1);\n        }\n        \n        h1 {\n            font-size: 2.5rem;\n            margin: 0;\n            padding: 0;\n        }\n        \n        h2 {\n            color: var(--medium-text);\n            border-bottom: 2px solid var(--accent-color);\n            padding-bottom: 10px;\n            margin-top: 40px;\n        }\n        \n        h3 {\n            color: var(--light-text);\n        }\n        \n        .card {\n            background-color: white;\n            border-radius: 12px;\n            padding: 20px;\n            margin-bottom: 30px;\n            box-shadow: 0 5px 15px rgba(0,0,0,0.05);\n            transition: transform 0.3s, box-shadow 0.3s;\n        }\n        \n        .card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n        }\n        \n        .keyword-tag {\n            display: inline-block;\n            background-color: var(--accent-color);\n            color: var(--dark-text);\n            padding: 5px 15px;\n            border-radius: 20px;\n            margin: 5px;\n            font-weight: 500;\n            box-shadow: 0 2px 5px rgba(0,0,0,0.1);\n        }\n        \n        .message-bubble {\n            max-width: 80%;\n            padding: 12px 18px;\n            border-radius: 18px;\n            margin-bottom: 10px;\n            position: relative;\n        }\n        \n        .message-left {\n            background-color: #FFEDD5;\n            margin-right: auto;\n            border-top-left-radius: 5px;\n        }\n        \n        .message-right {\n            background-color: #FFE0B2;\n            margin-left: auto;\n            border-top-right-radius: 5px;\n        }\n        \n        .speaker-info {\n            font-size: 0.8rem;\n            color: var(--light-text);\n            margin-bottom: 5px;\n        }\n        \n        .quote-card {\n            background-color: #FFF9E6;\n            border-left: 4px solid var(--secondary-color);\n            padding: 15px;\n            margin-bottom: 15px;\n            border-radius: 0 8px 8px 0;\n        }\n        \n        .quote-text {\n            font-style: italic;\n            font-size: 1.1rem;\n            color: var(--dark-text);\n        }\n        \n        .quote-author {\n            text-align: right;\n            font-size: 0.9rem;\n            color: var(--light-text);\n        }\n        \n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n            gap: 20px;\n            margin-bottom: 30px;\n        }\n        \n        .stat-item {\n            background-color: white;\n            border-radius: 12px;\n            padding: 20px;\n            text-align: center;\n            box-shadow: 0 5px 15px rgba(0,0,0,0.05);\n        }\n        \n        .stat-number {\n            font-size: 2.5rem;\n            font-weight: bold;\n            color: var(--primary-color);\n            margin: 10px 0;\n        }\n        \n        .stat-label {\n            font-size: 1rem;\n            color: var(--light-text);\n        }\n        \n        .mermaid {\n            background-color: white;\n            padding: 20px;\n            border-radius: 12px;\n            margin: 20px 0;\n        }\n        \n        .user-activity {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 15px;\n            margin-bottom: 30px;\n        }\n        \n        .user-card {\n            background-color: white;\n            border-radius: 12px;\n            padding: 15px;\n            flex: 1;\n            min-width: 200px;\n            box-shadow: 0 5px 15px rgba(0,0,0,0.05);\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n        }\n        \n        .user-avatar {\n            width: 60px;\n            height: 60px;\n            background-color: var(--accent-color);\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: white;\n            font-size: 1.5rem;\n            margin-bottom: 10px;\n        }\n        \n        .user-name {\n            font-weight: bold;\n            margin-bottom: 5px;\n        }\n        \n        .user-messages {\n            font-size: 0.9rem;\n            color: var(--light-text);\n        }\n        \n        @media (max-width: 768px) {\n            .stats-grid {\n                grid-template-columns: 1fr;\n            }\n            \n            h1 {\n                font-size: 2rem;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header>\n            <h1>Fingerfly AIGC嘉宾群</h1>\n            <p>2025年06月15日 聊天精华报告</p>\n        </header>\n        \n        <div class=\"stats-grid\">\n            <div class=\"stat-item\">\n                <i class=\"fas fa-comments\" style=\"font-size: 2rem; color: var(--secondary-color);\"></i>\n                <div class=\"stat-number\">18</div>\n                <div class=\"stat-label\">消息总数</div>\n            </div>\n            <div class=\"stat-item\">\n                <i class=\"fas fa-users\" style=\"font-size: 2rem; color: var(--secondary-color);\"></i>\n                <div class=\"stat-number\">9</div>\n                <div class=\"stat-label\">活跃用户</div>\n            </div>\n            <div class=\"stat-item\">\n                <i class=\"fas fa-clock\" style=\"font-size: 2rem; color: var(--secondary-color);\"></i>\n                <div class=\"stat-label\">19:50 - 21:58</div>\n                <div class=\"stat-label\">讨论时长</div>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>核心关键词速览</h2>\n            <div>\n                <span class=\"keyword-tag\">支持</span>\n                <span class=\"keyword-tag\">预售</span>\n                <span class=\"keyword-tag\">参与</span>\n                <span class=\"keyword-tag\">准备</span>\n                <span class=\"keyword-tag\">教程</span>\n                <span class=\"keyword-tag\">配合</span>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>核心概念关系图</h2>\n            <div class=\"mermaid\">\n                flowchart LR\n                    A[支持] --> B[参与]\n                    B --> C[准备]\n                    C --> D[预售]\n                    D --> E[教程]\n                    E --> F[配合]\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>活跃用户分析</h2>\n            <div class=\"user-activity\">\n                <div class=\"user-card\">\n                    <div class=\"user-avatar\">C</div>\n                    <div class=\"user-name\">Cydiar</div>\n                    <div class=\"user-messages\">4条消息</div>\n                </div>\n                <div class=\"user-card\">\n                    <div class=\"user-avatar\">汗</div>\n                    <div class=\"user-name\">汗青</div>\n                    <div class=\"user-messages\">3条消息</div>\n                </div>\n                <div class=\"user-card\">\n                    <div class=\"user-avatar\">向</div>\n                    <div class=\"user-name\">向阳乔木</div>\n                    <div class=\"user-messages\">3条消息</div>\n                </div>\n                <div class=\"user-card\">\n                    <div class=\"user-avatar\">李</div>\n                    <div class=\"user-name\">李福祥</div>\n                    <div class=\"user-messages\">2条消息</div>\n                </div>\n            </div>\n            <canvas id=\"userActivityChart\"></canvas>\n        </div>\n        \n        <div class=\"card\">\n            <h2>精华话题聚焦</h2>\n            \n            <h3>项目支持与参与</h3>\n            <p>群组成员讨论了一个项目的支持和参与方式，多位成员表达了积极的参与意愿和具体的支持方案。</p>\n            \n            <div class=\"speaker-info\">Cydiar 19:55:29</div>\n            <div class=\"message-bubble message-left\">没问题，搞。</div>\n            \n            <div class=\"speaker-info\">Cydiar 19:55:55</div>\n            <div class=\"message-bubble message-left\">我送啊。</div>\n            \n            <div class=\"speaker-info\">Cydiar 19:56:05</div>\n            <div class=\"message-bubble message-left\">几十个没问题。</div>\n            \n            <div class=\"speaker-info\">指尖泛出的繁华 19:57:04</div>\n            <div class=\"message-bubble message-right\">哈哈没问题，免费支持[让我看看]</div>\n            \n            <h3 style=\"margin-top: 30px;\">项目筹备与时间安排</h3>\n            <p>讨论转向了项目的具体筹备工作和时间安排，成员们分享了各自的准备情况和时间考虑。</p>\n            \n            <div class=\"speaker-info\">向阳乔木 21:50:43</div>\n            <div class=\"message-bubble message-left\">这两天合计下，不知道来不来得及。</div>\n            \n            <div class=\"speaker-info\">Cydiar 21:58:14</div>\n            <div class=\"message-bubble message-left\">你搞吧，我准备好了。</div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>群友金句闪耀</h2>\n            \n            <div class=\"quote-card\">\n                <div class=\"quote-text\">\"几十个没问题。\"</div>\n                <div class=\"quote-author\">— Cydiar, 19:56:05</div>\n            </div>\n            \n            <div class=\"quote-card\">\n                <div class=\"quote-text\">\"哈哈没问题，免费支持\"</div>\n                <div class=\"quote-author\">— 指尖泛出的繁华, 19:57:04</div>\n            </div>\n            \n            <div class=\"quote-card\">\n                <div class=\"quote-text\">\"想参与，但我只有个姑娘…\"</div>\n                <div class=\"quote-author\">— 汗青, 20:21:35</div>\n            </div>\n            \n            <div class=\"quote-card\">\n                <div class=\"quote-text\">\"你搞吧，我准备好了。\"</div>\n                <div class=\"quote-author\">— Cydiar, 21:58:14</div>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>消息时间分布</h2>\n            <canvas id=\"messageTimelineChart\"></canvas>\n        </div>\n    </div>\n\n    <script>\n        // 用户活跃度图表\n        const userActivityCtx = document.getElementById('userActivityChart').getContext('2d');\n        const userActivityChart = new Chart(userActivityCtx, {\n            type: 'bar',\n            data: {\n                labels: ['Cydiar', '汗青', '向阳乔木', '李福祥', 'Super黄', '歸藏', '大聪明', 'Owen', '指尖泛出的繁华'],\n                datasets: [{\n                    label: '消息数量',\n                    data: [4, 3, 3, 2, 1, 1, 1, 1, 1],\n                    backgroundColor: [\n                        '#FF7E5F',\n                        '#FFB347',\n                        '#FFD166',\n                        '#FFED8A',\n                        '#FFA07A',\n                        '#FF8C66',\n                        '#FFB77A',\n                        '#FFD08A',\n                        '#FFE39E'\n                    ],\n                    borderColor: [\n                        '#E56B4F',\n                        '#E59F3D',\n                        '#E5B95D',\n                        '#E5D479',\n                        '#E58C6A',\n                        '#E57A56',\n                        '#E5A36A',\n                        '#E5B879',\n                        '#E5CD8E'\n                    ],\n                    borderWidth: 1\n                }]\n            },\n            options: {\n                responsive: true,\n                plugins: {\n                    legend: {\n                        display: false\n                    },\n                    title: {\n                        display: true,\n                        text: '用户消息数量分布',\n                        font: {\n                            size: 16\n                        }\n                    }\n                },\n                scales: {\n                    y: {\n                        beginAtZero: true,\n                        ticks: {\n                            stepSize: 1\n                        }\n                    }\n                }\n            }\n        });\n        \n        // 消息时间线图表\n        const timelineCtx = document.getElementById('messageTimelineChart').getContext('2d');\n        const timelineChart = new Chart(timelineCtx, {\n            type: 'line',\n            data: {\n                labels: ['19:50', '19:55', '20:00', '20:05', '20:20', '20:25', '20:30', '20:45', '21:40', '21:50', '21:55'],\n                datasets: [{\n                    label: '消息数量',\n                    data: [1, 3, 1, 0, 2, 1, 2, 1, 1, 1, 1],\n                    fill: false,\n                    backgroundColor: '#FF7E5F',\n                    borderColor: '#FF7E5F',\n                    tension: 0.1\n                }]\n            },\n            options: {\n                responsive: true,\n                plugins: {\n                    legend: {\n                        display: false\n                    },\n                    title: {\n                        display: true,\n                        text: '消息时间分布',\n                        font: {\n                            size: 16\n                        }\n                    }\n                },\n                scales: {\n                    y: {\n                        beginAtZero: true,\n                        ticks: {\n                            stepSize: 1\n                        }\n                    }\n                }\n            }\n        });\n        \n        // 初始化Mermaid\n        mermaid.initialize({\n            startOnLoad: true,\n            theme: 'default',\n            themeVariables: {\n                primaryColor: '#FFE0B2',\n                nodeBorder: '#FFB347',\n                lineColor: '#FF7E5F',\n                textColor: '#5C4033'\n            }\n        });\n    </script>\n</body>\n</html>", "savedAt": "2025-06-16T12:52:05.615Z"}