{"groupName": "AI-Native产品&技术交流", "analysisType": "dynamic_1750063573196", "timeRange": "2025-06-15", "messageCount": 3, "timestamp": "2025-06-16T14:45:37.354Z", "title": "AI-Native产品&技术交流 - 聊天数据分析", "content": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>AI-Native产品&技术交流 - 2025年06月15日 聊天精华报告</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js\"></script>\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n    <style>\n        :root {\n            --primary: #FF9A3C;\n            --secondary: #FF6B6B;\n            --accent: #FFD166;\n            --light: #FFF5E6;\n            --dark: #5C4033;\n            --text: #5C4033;\n            --text-light: #8B6B4D;\n        }\n        \n        body {\n            font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", <PERSON><PERSON>, \"Helvetica Neue\", <PERSON><PERSON>, \"Noto Sans SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"WenQuanYi Micro Hei\", sans-serif;\n            background-color: var(--light);\n            color: var(--text);\n            line-height: 1.6;\n            margin: 0;\n            padding: 0;\n        }\n        \n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n            padding: 20px;\n        }\n        \n        header {\n            text-align: center;\n            margin-bottom: 40px;\n            padding: 20px;\n            background: linear-gradient(135deg, rgba(255,154,60,0.1) 0%, rgba(255,214,102,0.1) 100%);\n            border-radius: 15px;\n            box-shadow: 0 4px 15px rgba(0,0,0,0.05);\n        }\n        \n        h1 {\n            color: var(--dark);\n            font-size: 2.5rem;\n            margin-bottom: 10px;\n        }\n        \n        .subtitle {\n            color: var(--text-light);\n            font-size: 1.2rem;\n            margin-bottom: 20px;\n        }\n        \n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 20px;\n            margin-bottom: 40px;\n        }\n        \n        .stat-card {\n            background-color: white;\n            border-radius: 12px;\n            padding: 20px;\n            box-shadow: 0 4px 10px rgba(0,0,0,0.05);\n            text-align: center;\n            transition: transform 0.3s ease;\n        }\n        \n        .stat-card:hover {\n            transform: translateY(-5px);\n        }\n        \n        .stat-value {\n            font-size: 2.5rem;\n            font-weight: bold;\n            color: var(--primary);\n            margin: 10px 0;\n        }\n        \n        .stat-label {\n            color: var(--text-light);\n            font-size: 1rem;\n        }\n        \n        .keyword-cloud {\n            display: flex;\n            flex-wrap: wrap;\n            justify-content: center;\n            gap: 10px;\n            margin: 30px 0;\n        }\n        \n        .keyword-tag {\n            background-color: var(--accent);\n            color: var(--dark);\n            padding: 8px 15px;\n            border-radius: 20px;\n            font-weight: 600;\n            box-shadow: 0 2px 5px rgba(0,0,0,0.1);\n            transition: all 0.3s ease;\n        }\n        \n        .keyword-tag:hover {\n            background-color: var(--primary);\n            transform: scale(1.05);\n        }\n        \n        .section {\n            background-color: white;\n            border-radius: 15px;\n            padding: 30px;\n            margin-bottom: 40px;\n            box-shadow: 0 4px 15px rgba(0,0,0,0.05);\n        }\n        \n        .section-title {\n            color: var(--primary);\n            font-size: 1.8rem;\n            margin-top: 0;\n            margin-bottom: 25px;\n            padding-bottom: 10px;\n            border-bottom: 2px solid var(--light);\n        }\n        \n        .mermaid-container {\n            background-color: #FFF9F0;\n            padding: 20px;\n            border-radius: 10px;\n            margin: 20px 0;\n            min-height: 300px;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n        }\n        \n        .message-container {\n            margin: 30px 0;\n        }\n        \n        .message {\n            max-width: 70%;\n            margin-bottom: 15px;\n            padding: 15px;\n            border-radius: 15px;\n            position: relative;\n        }\n        \n        .message-left {\n            background-color: #FFEDD5;\n            margin-right: auto;\n            border-top-left-radius: 5px;\n        }\n        \n        .message-right {\n            background-color: #FFE0B2;\n            margin-left: auto;\n            border-top-right-radius: 5px;\n        }\n        \n        .message-info {\n            font-size: 0.8rem;\n            color: var(--text-light);\n            margin-bottom: 5px;\n        }\n        \n        .message-content {\n            font-size: 1rem;\n        }\n        \n        .quote-card {\n            background-color: #FFF3E0;\n            border-radius: 15px;\n            padding: 20px;\n            margin-bottom: 20px;\n            border-left: 5px solid var(--primary);\n            transition: all 0.3s ease;\n        }\n        \n        .quote-card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 10px 20px rgba(0,0,0,0.1);\n        }\n        \n        .quote-text {\n            font-size: 1.2rem;\n            font-style: italic;\n            color: var(--dark);\n            margin-bottom: 10px;\n        }\n        \n        .quote-author {\n            font-size: 0.9rem;\n            color: var(--text-light);\n            text-align: right;\n        }\n        \n        .timeline {\n            position: relative;\n            max-width: 800px;\n            margin: 0 auto;\n        }\n        \n        .timeline::after {\n            content: '';\n            position: absolute;\n            width: 2px;\n            background-color: var(--accent);\n            top: 0;\n            bottom: 0;\n            left: 50%;\n            margin-left: -1px;\n        }\n        \n        .timeline-item {\n            padding: 10px 40px;\n            position: relative;\n            width: 50%;\n            box-sizing: border-box;\n        }\n        \n        .timeline-item::after {\n            content: '';\n            position: absolute;\n            width: 20px;\n            height: 20px;\n            background-color: white;\n            border: 4px solid var(--primary);\n            border-radius: 50%;\n            top: 15px;\n            z-index: 1;\n        }\n        \n        .left {\n            left: 0;\n        }\n        \n        .right {\n            left: 50%;\n        }\n        \n        .left::after {\n            right: -12px;\n        }\n        \n        .right::after {\n            left: -12px;\n        }\n        \n        .timeline-content {\n            background-color: white;\n            padding: 20px;\n            border-radius: 10px;\n            box-shadow: 0 4px 8px rgba(0,0,0,0.1);\n        }\n        \n        .timeline-time {\n            color: var(--primary);\n            font-weight: bold;\n        }\n        \n        .timeline-user {\n            font-weight: bold;\n            margin: 5px 0;\n        }\n        \n        footer {\n            text-align: center;\n            padding: 20px;\n            color: var(--text-light);\n            font-size: 0.9rem;\n        }\n        \n        @media (max-width: 768px) {\n            .timeline::after {\n                left: 31px;\n            }\n            \n            .timeline-item {\n                width: 100%;\n                padding-left: 70px;\n                padding-right: 25px;\n            }\n            \n            .timeline-item::after {\n                left: 21px;\n            }\n            \n            .left::after, .right::after {\n                left: 21px;\n            }\n            \n            .right {\n                left: 0;\n            }\n            \n            .message {\n                max-width: 85%;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header>\n            <h1>AI-Native产品&技术交流</h1>\n            <div class=\"subtitle\">2025年06月15日 聊天精华报告</div>\n        </header>\n        \n        <div class=\"stats-grid\">\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">3</div>\n                <div class=\"stat-label\">消息总数</div>\n            </div>\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">2</div>\n                <div class=\"stat-label\">活跃用户</div>\n            </div>\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">2</div>\n                <div class=\"stat-label\">有效消息</div>\n            </div>\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">4分钟</div>\n                <div class=\"stat-label\">讨论时长</div>\n            </div>\n        </div>\n        \n        <div class=\"section\">\n            <h2 class=\"section-title\"><i class=\"fas fa-tags\"></i> 核心关键词速览</h2>\n            <div class=\"keyword-cloud\">\n                <span class=\"keyword-tag\">投资人活动</span>\n                <span class=\"keyword-tag\">报名</span>\n                <span class=\"keyword-tag\">投资机构</span>\n                <span class=\"keyword-tag\">AI</span>\n                <span class=\"keyword-tag\">机器人</span>\n                <span class=\"keyword-tag\">web3</span>\n            </div>\n        </div>\n        \n        <div class=\"section\">\n            <h2 class=\"section-title\"><i class=\"fas fa-project-diagram\"></i> 核心概念关系图</h2>\n            <div class=\"mermaid-container\">\n                <div class=\"mermaid\">\n                    flowchart LR\n                    A[投资人活动] --> B[报名]\n                    A --> C[投资机构]\n                    C --> D[PAG太盟投资]\n                    C --> E[软银中国]\n                    C --> F[IDG资本]\n                    A --> G[行业领域]\n                    G --> H[AI]\n                    G --> I[机器人]\n                    G --> J[web3]\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"section\">\n            <h2 class=\"section-title\"><i class=\"fas fa-comments\"></i> 精华对话</h2>\n            <div class=\"message-container\">\n                <div class=\"message message-left\">\n                    <div class=\"message-info\">2025-06-15 20:42:50 · Big fans of Jazz</div>\n                    <div class=\"message-content\">\n                        ［转］报名倒计时6天‼️<br>\n                        【周六投资人活动已报名60+】<br>\n                        参与部分投资人名单：PAG太盟投资、软银中国、金浦投资、IDG资本、高盛、兆龙互连、盈确控股、晨熹资本、百联商投、拙朴投资、Saintander Partners（海外）、驼峰资本等等、晶科战投、仓廪资本、浩悦资本、星海控股/华懋科技CVC 、蓝图创投、鹏安基金、民生证券投行部等等。涵盖美元资本、产业资本、上市公司CVC、以及人民币基金；以及AI、机器人、web3相关企业创始人。<br><br>\n                        位置有限了，欲报名从速！\n                    </div>\n                </div>\n                \n                <div class=\"message message-right\">\n                    <div class=\"message-info\">2025-06-15 20:46:08 · Peng</div>\n                    <div class=\"message-content\">\n                        私信你了\n                    </div>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"section\">\n            <h2 class=\"section-title\"><i class=\"fas fa-quote-left\"></i> 群友金句</h2>\n            <div class=\"quote-card\">\n                <div class=\"quote-text\">\n                    \"位置有限了，欲报名从速！\"\n                </div>\n                <div class=\"quote-author\">— Big fans of Jazz · 2025-06-15 20:42:50</div>\n            </div>\n        </div>\n        \n        <div class=\"section\">\n            <h2 class=\"section-title\"><i class=\"fas fa-chart-line\"></i> 活动数据分析</h2>\n            <canvas id=\"activityChart\" height=\"200\"></canvas>\n        </div>\n        \n        <div class=\"section\">\n            <h2 class=\"section-title\"><i class=\"fas fa-history\"></i> 时间线</h2>\n            <div class=\"timeline\">\n                <div class=\"timeline-item left\">\n                    <div class=\"timeline-content\">\n                        <div class=\"timeline-time\">20:42:50</div>\n                        <div class=\"timeline-user\">Big fans of Jazz</div>\n                        <p>分享了投资人活动信息，包含60+报名者和众多知名投资机构</p>\n                    </div>\n                </div>\n                <div class=\"timeline-item right\">\n                    <div class=\"timeline-content\">\n                        <div class=\"timeline-time\">20:46:08</div>\n                        <div class=\"timeline-user\">Peng</div>\n                        <p>表示已私信联系</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n        \n        <footer>\n            <p>报告生成时间: 2025-06-15 | 数据来源: AI-Native产品&技术交流群聊</p>\n        </footer>\n    </div>\n    \n    <script>\n        mermaid.initialize({\n            startOnLoad: true,\n            theme: 'default',\n            themeVariables: {\n                primaryColor: '#FFE0B2',\n                nodeBorder: '#FF9A3C',\n                lineColor: '#FF9A3C',\n                textColor: '#5C4033'\n            }\n        });\n        \n        // 活动数据图表\n        const ctx = document.getElementById('activityChart').getContext('2d');\n        const activityChart = new Chart(ctx, {\n            type: 'bar',\n            data: {\n                labels: ['报名人数', '投资机构', '行业领域'],\n                datasets: [{\n                    label: '活动数据统计',\n                    data: [60, 15, 3],\n                    backgroundColor: [\n                        'rgba(255, 154, 60, 0.7)',\n                        'rgba(255, 107, 107, 0.7)',\n                        'rgba(255, 209, 102, 0.7)'\n                    ],\n                    borderColor: [\n                        'rgba(255, 154, 60, 1)',\n                        'rgba(255, 107, 107, 1)',\n                        'rgba(255, 209, 102, 1)'\n                    ],\n                    borderWidth: 1\n                }]\n            },\n            options: {\n                responsive: true,\n                scales: {\n                    y: {\n                        beginAtZero: true\n                    }\n                },\n                plugins: {\n                    legend: {\n                        position: 'top',\n                    },\n                    title: {\n                        display: true,\n                        text: '投资人活动关键数据',\n                        font: {\n                            size: 16\n                        }\n                    }\n                }\n            }\n        });\n    </script>\n</body>\n</html>", "savedAt": "2025-06-16T14:45:37.354Z"}