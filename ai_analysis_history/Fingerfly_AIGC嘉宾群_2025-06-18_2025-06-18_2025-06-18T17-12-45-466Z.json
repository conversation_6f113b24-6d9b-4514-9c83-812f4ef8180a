{"title": "[定时] 自定义分析 - Fingerfly AIGC嘉宾群", "groupName": "Fingerfly AIGC嘉宾群", "analysisType": "custom", "timeRange": "2025-06-18~2025-06-18", "messageCount": 213, "isScheduled": true, "content": "```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Fingerfly AIGC嘉宾群 聊天数据分析</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js\"></script>\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n    <style>\n        :root {\n            --amber-50: #fffbeb;\n            --amber-100: #fef3c7;\n            --amber-200: #fde68a;\n            --amber-300: #fcd34d;\n            --amber-700: #b45309;\n            --orange-100: #ffedd5;\n            --orange-300: #fdba74;\n            --orange-500: #f97316;\n            --orange-700: #c2410c;\n            --stone-50: #fafaf9;\n            --stone-100: #f5f5f4;\n            --stone-700: #44403c;\n            --stone-800: #292524;\n        }\n        \n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n        \n        body {\n            font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", \n                         Arial, \"Noto Sans SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", sans-serif;\n            background-color: var(--amber-50);\n            color: var(--stone-800);\n            line-height: 1.6;\n            padding: 20px;\n        }\n        \n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n        }\n        \n        header {\n            text-align: center;\n            padding: 30px 0;\n            margin-bottom: 30px;\n            border-bottom: 2px solid var(--orange-300);\n        }\n        \n        h1 {\n            font-size: 2.5rem;\n            color: var(--orange-700);\n            margin-bottom: 10px;\n        }\n        \n        .subtitle {\n            font-size: 1.2rem;\n            color: var(--stone-700);\n        }\n        \n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 20px;\n            margin-bottom: 40px;\n        }\n        \n        .stat-card {\n            background: white;\n            border-radius: 12px;\n            padding: 20px;\n            box-shadow: 0 4px 6px rgba(0,0,0,0.05);\n            text-align: center;\n            transition: transform 0.3s ease;\n        }\n        \n        .stat-card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 6px 12px rgba(0,0,0,0.1);\n        }\n        \n        .stat-value {\n            font-size: 2.5rem;\n            font-weight: bold;\n            color: var(--orange-500);\n            margin: 10px 0;\n        }\n        \n        .keyword-tag {\n            display: inline-block;\n            background: var(--orange-100);\n            color: var(--orange-700);\n            padding: 6px 12px;\n            border-radius: 20px;\n            margin: 5px;\n            font-size: 0.9rem;\n            font-weight: 500;\n        }\n        \n        .chart-container {\n            background: white;\n            border-radius: 12px;\n            padding: 25px;\n            margin: 30px 0;\n            box-shadow: 0 4px 6px rgba(0,0,0,0.05);\n        }\n        \n        .bento-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n            gap: 25px;\n            margin: 40px 0;\n        }\n        \n        .bento-card {\n            background: white;\n            border-radius: 12px;\n            padding: 25px;\n            box-shadow: 0 4px 6px rgba(0,0,0,0.05);\n            transition: all 0.3s ease;\n        }\n        \n        .bento-card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 8px 15px rgba(0,0,0,0.1);\n        }\n        \n        .bento-card h3 {\n            color: var(--orange-700);\n            margin-bottom: 15px;\n            display: flex;\n            align-items: center;\n            gap: 10px;\n        }\n        \n        .bento-card h3 i {\n            color: var(--orange-500);\n        }\n        \n        .message-bubble {\n            padding: 12px 15px;\n            border-radius: 18px;\n            margin: 10px 0;\n            max-width: 80%;\n            position: relative;\n        }\n        \n        .user-left {\n            background: var(--amber-100);\n            margin-right: auto;\n            border-top-left-radius: 5px;\n        }\n        \n        .user-right {\n            background: var(--orange-100);\n            margin-left: auto;\n            border-top-right-radius: 5px;\n        }\n        \n        .speaker-info {\n            font-size: 0.85rem;\n            color: var(--stone-700);\n            margin-bottom: 5px;\n            font-weight: 500;\n        }\n        \n        .dialogue-content {\n            font-size: 1rem;\n        }\n        \n        .quote-card {\n            background: var(--amber-100);\n            border-left: 4px solid var(--orange-500);\n            padding: 15px 20px;\n            margin: 15px 0;\n            border-radius: 0 8px 8px 0;\n        }\n        \n        .quote-text {\n            font-size: 1.1rem;\n            font-style: italic;\n            margin-bottom: 10px;\n        }\n        \n        .quote-highlight {\n            color: var(--orange-700);\n            font-weight: bold;\n        }\n        \n        .quote-author {\n            text-align: right;\n            font-size: 0.9rem;\n            color: var(--stone-700);\n            font-weight: 500;\n        }\n        \n        .mermaid-container {\n            background: var(--stone-50);\n            padding: 20px;\n            border-radius: 10px;\n            margin: 20px 0;\n            overflow: auto;\n        }\n        \n        footer {\n            text-align: center;\n            padding: 30px 0;\n            margin-top: 40px;\n            color: var(--stone-700);\n            font-size: 0.9rem;\n            border-top: 1px solid var(--orange-300);\n        }\n        \n        @media (max-width: 768px) {\n            .bento-grid {\n                grid-template-columns: 1fr;\n            }\n            \n            .stats-grid {\n                grid-template-columns: 1fr;\n            }\n            \n            h1 {\n                font-size: 2rem;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header>\n            <h1><i class=\"fas fa-comments\"></i> Fingerfly AIGC嘉宾群 聊天分析报告</h1>\n            <p class=\"subtitle\">2025年6月18日 | 消息总数: 213 | 活跃用户: 13人</p>\n            \n            <div class=\"keywords\" style=\"margin-top: 20px;\">\n                <span class=\"keyword-tag\">AI内容创作</span>\n                <span class=\"keyword-tag\">信息差变现</span>\n                <span class=\"keyword-tag\">私域流量</span>\n                <span class=\"keyword-tag\">创业挑战</span>\n                <span class=\"keyword-tag\">产品开发</span>\n                <span class=\"keyword-tag\">内容倦怠</span>\n            </div>\n        </header>\n        \n        <div class=\"stats-grid\">\n            <div class=\"stat-card\">\n                <i class=\"fas fa-user fa-2x\"></i>\n                <h3>活跃用户</h3>\n                <div class=\"stat-value\">13</div>\n            </div>\n            \n            <div class=\"stat-card\">\n                <i class=\"fas fa-comment fa-2x\"></i>\n                <h3>消息总数</h3>\n                <div class=\"stat-value\">213</div>\n            </div>\n            \n            <div class=\"stat-card\">\n                <i class=\"fas fa-clock fa-2x\"></i>\n                <h3>持续时间</h3>\n                <div class=\"stat-value\">13.4h</div>\n            </div>\n            \n            <div class=\"stat-card\">\n                <i class=\"fas fa-star fa-2x\"></i>\n                <h3>高峰时段</h3>\n                <div class=\"stat-value\">19:00-23:00</div>\n            </div>\n        </div>\n        \n        <div class=\"chart-container\">\n            <h2><i class=\"fas fa-chart-bar\"></i> 用户活跃度分析</h2>\n            <canvas id=\"userActivityChart\" height=\"350\"></canvas>\n        </div>\n        \n        <div class=\"mermaid-container\">\n            <h2><i class=\"fas fa-project-diagram\"></i> 核心概念关系图</h2>\n            <div class=\"mermaid\">\nflowchart LR\n    A[AI内容创作] --> B[信息差变现]\n    A --> C[私域流量]\n    B --> D[行业知识库]\n    C --> E[内容倦怠]\n    D --> F[传统行业+AI]\n    E --> G[高质量内容稀缺]\n    G --> H[创业挑战]\n    H --> I[产品开发]\n    I --> J[团队协作]\n            </div>\n        </div>\n        \n        <div class=\"bento-grid\">\n            <div class=\"bento-card\">\n                <h3><i class=\"fas fa-comment-dots\"></i> 精选对话分析</h3>\n                \n                <div class=\"message-bubble user-left\">\n                    <div class=\"speaker-info\">向阳乔木 (22:36)</div>\n                    <div class=\"dialogue-content\">感觉靠信息差，他们的行业老板简单试用下，觉得回答靠谱就买了。</div>\n                </div>\n                \n                <div class=\"message-bubble user-right\">\n                    <div class=\"speaker-info\">Leo🍊Orange AI (22:36)</div>\n                    <div class=\"dialogue-content\">这个时代好的信息是值得付费的，我感觉年轻人正在叛逃抖音。</div>\n                </div>\n                \n                <div class=\"message-bubble user-left\">\n                    <div class=\"speaker-info\">小互 (22:37)</div>\n                    <div class=\"dialogue-content\">中文互联网内容根本没法看，AI搜索出来也没啥用，所以会进入另一个阶段。</div>\n                </div>\n                \n                <div class=\"message-bubble user-right\">\n                    <div class=\"speaker-info\">Cydiar (22:40)</div>\n                    <div class=\"dialogue-content\">这他妈的 tavily 把我看傻了！完全不知道吹这么欢到底是咋。</div>\n                </div>\n            </div>\n            \n            <div class=\"bento-card\">\n                <h3><i class=\"fas fa-lightbulb\"></i> 群友金句</h3>\n                \n                <div class=\"quote-card\">\n                    <div class=\"quote-text\">\"<span class=\"quote-highlight\">传统行业+AI</span>厉害。领导力课程会员搭伙做了医疗销售知识库，分分钟开启收费赚钱\"</div>\n                    <div class=\"quote-author\">— 向阳乔木 (22:28)</div>\n                </div>\n                \n                <div class=\"quote-card\">\n                    <div class=\"quote-text\">\"<span class=\"quote-highlight\">好的信息是值得付费的</span>，我感觉年轻人正在叛逃抖音\"</div>\n                    <div class=\"quote-author\">— Leo🍊Orange AI (22:36)</div>\n                </div>\n                \n                <div class=\"quote-card\">\n                    <div class=\"quote-text\">\"除了Juchats我还有<span class=\"quote-highlight\">6个B端项目</span>...只能2B养\"</div>\n                    <div class=\"quote-author\">— Cydiar (22:43)</div>\n                </div>\n                \n                <div class=\"quote-card\">\n                    <div class=\"quote-text\">\"<span class=\"quote-highlight\">白天要带娃，晚上敲代码</span>，三个新晋奶爸\"</div>\n                    <div class=\"quote-author\">— Cydiar (22:45)</div>\n                </div>\n            </div>\n            \n            <div class=\"bento-card\">\n                <h3><i class=\"fas fa-link\"></i> 提及资源</h3>\n                <ul style=\"list-style-type: none; padding: 15px 0;\">\n                    <li style=\"padding: 10px 0; border-bottom: 1px dashed #eee;\">\n                        <i class=\"fas fa-external-link-alt\" style=\"color: var(--orange-500);\"></i>\n                        <strong>Juchats</strong> - AI聊天聚合平台<br>\n                        <a href=\"https://www.juchats.com/\" target=\"_blank\">https://www.juchats.com/</a>\n                    </li>\n                    <li style=\"padding: 10px 0; border-bottom: 1px dashed #eee;\">\n                        <i class=\"fas fa-external-link-alt\" style=\"color: var(--orange-500);\"></i>\n                        <strong>参考答案</strong> - 资料整理账号<br>\n                        <span>小红书号：参考答案</span>\n                    </li>\n                    <li style=\"padding: 10px 0;\">\n                        <i class=\"fas fa-external-link-alt\" style=\"color: var(--orange-500);\"></i>\n                        <strong>年度计费策略</strong><br>\n                        <a href=\"https://open.substack.com/pub/kylepoyar/p/how-to-sell-annual-plans?r=3ed0r8&utm_campaign=post&utm_medium=email\" target=\"_blank\">销售年度计划指南</a>\n                    </li>\n                </ul>\n            </div>\n        </div>\n        \n        <div class=\"chart-container\">\n            <h2><i class=\"fas fa-chart-line\"></i> 时段活跃分析</h2>\n            <canvas id=\"timeActivityChart\" height=\"350\"></canvas>\n        </div>\n        \n        <footer>\n            <p>Fingerfly AIGC嘉宾群 聊天数据分析报告 | 生成时间: 2025年6月19日</p>\n            <p>数据可视化分析 © 2025</p>\n        </footer>\n    </div>\n\n    <script>\n        // 初始化Mermaid\n        mermaid.initialize({\n            startOnLoad: true,\n            theme: 'base',\n            themeVariables: {\n                primaryColor: '#FDE68A',\n                nodeBorder: '#F59E0B',\n                lineColor: '#D97706',\n                textColor: '#78350F'\n            }\n        });\n        \n        // 用户活跃度数据\n        const userData = {\n            labels: ['向阳乔木', '小互', 'Leo🍊Orange AI', '汗青', 'Cydiar', '其他用户'],\n            datasets: [{\n                label: '发言数量',\n                data: [33, 32, 21, 20, 20, 87],\n                backgroundColor: [\n                    'rgba(231, 126, 34, 0.7)',\n                    'rgba(245, 158, 11, 0.7)',\n                    'rgba(249, 115, 22, 0.7)',\n                    'rgba(251, 146, 60, 0.7)',\n                    'rgba(253, 186, 116, 0.7)',\n                    'rgba(254, 215, 170, 0.7)'\n                ],\n                borderColor: [\n                    'rgb(194, 65, 12)',\n                    'rgb(180, 83, 9)',\n                    'rgb(194, 65, 12)',\n                    'rgb(180, 83, 9)',\n                    'rgb(194, 65, 12)',\n                    'rgb(180, 83, 9)'\n                ],\n                borderWidth: 1\n            }]\n        };\n        \n        // 时段活跃数据\n        const timeData = {\n            labels: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00'],\n            datasets: [{\n                label: '消息数量',\n                data: [0, 0, 0, 0, 3, 8, 2, 15, 5, 42, 68, 70],\n                fill: true,\n                backgroundColor: 'rgba(253, 230, 138, 0.2)',\n                borderColor: 'rgb(245, 158, 11)',\n                tension: 0.4,\n                pointBackgroundColor: 'rgb(194, 65, 12)'\n            }]\n        };\n        \n        // 渲染图表\n        window.onload = function() {\n            // 用户活跃度图表\n            const userCtx = document.getElementById('userActivityChart').getContext('2d');\n            new Chart(userCtx, {\n                type: 'bar',\n                data: userData,\n                options: {\n                    responsive: true,\n                    plugins: {\n                        legend: {\n                            display: false\n                        },\n                        title: {\n                            display: true,\n                            text: '用户发言数量分布',\n                            font: {\n                                size: 16\n                            }\n                        }\n                    },\n                    scales: {\n                        y: {\n                            beginAtZero: true,\n                            title: {\n                                display: true,\n                                text: '消息数量'\n                            }\n                        }\n                    }\n                }\n            });\n            \n            // 时段活跃图表\n            const timeCtx = document.getElementById('timeActivityChart').getContext('2d');\n            new Chart(timeCtx, {\n                type: 'line',\n                data: timeData,\n                options: {\n                    responsive: true,\n                    plugins: {\n                        title: {\n                            display: true,\n                            text: '各时段消息活跃度',\n                            font: {\n                                size: 16\n                            }\n                        }\n                    },\n                    scales: {\n                        y: {\n                            beginAtZero: true,\n                            title: {\n                                display: true,\n                                text: '消息数量'\n                            }\n                        },\n                        x: {\n                            title: {\n                                display: true,\n                                text: '时间'\n                            }\n                        }\n                    }\n                }\n            });\n        };\n    </script>\n</body>\n</html>\n```", "savedAt": "2025-06-18T17:12:45.466Z"}