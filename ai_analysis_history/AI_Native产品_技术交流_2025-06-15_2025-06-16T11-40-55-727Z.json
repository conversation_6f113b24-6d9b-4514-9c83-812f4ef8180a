{"groupName": "AI-Native产品&技术交流", "analysisType": "dynamic_1750063573196", "timeRange": "2025-06-15", "messageCount": 3, "timestamp": "2025-06-16T11:40:55.727Z", "title": "AI-Native产品&技术交流 - 聊天数据分析", "content": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>AI-Native产品&技术交流 - 2025年06月15日 聊天精华报告</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js\"></script>\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n    <style>\n        :root {\n            --primary-color: #FF9A3C;\n            --secondary-color: #FFC93C;\n            --accent-color: #FF6B6B;\n            --light-color: #FFF5EB;\n            --dark-color: #5C3D2E;\n            --text-color: #4A3A2F;\n            --light-text: #8B7D6B;\n        }\n        \n        body {\n            font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", <PERSON><PERSON>, \"Helvetica Neue\", <PERSON><PERSON>, \"Noto Sans SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"WenQuanYi Micro Hei\", sans-serif;\n            background-color: var(--light-color);\n            color: var(--text-color);\n            line-height: 1.6;\n            margin: 0;\n            padding: 0;\n        }\n        \n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n            padding: 20px;\n        }\n        \n        header {\n            text-align: center;\n            margin-bottom: 40px;\n            padding: 20px;\n            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n            border-radius: 15px;\n            color: white;\n            box-shadow: 0 10px 20px rgba(0,0,0,0.1);\n        }\n        \n        h1 {\n            font-size: 2.5rem;\n            margin-bottom: 10px;\n        }\n        \n        .subtitle {\n            font-size: 1.2rem;\n            opacity: 0.9;\n        }\n        \n        .stats-container {\n            display: flex;\n            justify-content: space-around;\n            flex-wrap: wrap;\n            margin: 30px 0;\n        }\n        \n        .stat-card {\n            background: white;\n            border-radius: 12px;\n            padding: 20px;\n            width: 22%;\n            min-width: 200px;\n            margin: 10px;\n            text-align: center;\n            box-shadow: 0 5px 15px rgba(0,0,0,0.05);\n            transition: transform 0.3s ease;\n        }\n        \n        .stat-card:hover {\n            transform: translateY(-5px);\n        }\n        \n        .stat-value {\n            font-size: 2.5rem;\n            font-weight: bold;\n            color: var(--primary-color);\n            margin: 10px 0;\n        }\n        \n        .stat-label {\n            color: var(--light-text);\n            font-size: 1rem;\n        }\n        \n        .section {\n            background: white;\n            border-radius: 15px;\n            padding: 25px;\n            margin-bottom: 30px;\n            box-shadow: 0 5px 15px rgba(0,0,0,0.05);\n        }\n        \n        .section-title {\n            color: var(--primary-color);\n            border-bottom: 2px solid var(--secondary-color);\n            padding-bottom: 10px;\n            margin-bottom: 20px;\n            font-size: 1.8rem;\n        }\n        \n        .keyword-tag {\n            display: inline-block;\n            background-color: var(--secondary-color);\n            color: var(--dark-color);\n            padding: 8px 15px;\n            margin: 5px;\n            border-radius: 20px;\n            font-weight: 600;\n            font-size: 0.9rem;\n            box-shadow: 0 3px 6px rgba(0,0,0,0.1);\n        }\n        \n        .message-container {\n            margin: 20px 0;\n        }\n        \n        .message {\n            max-width: 70%;\n            padding: 15px;\n            border-radius: 15px;\n            margin-bottom: 10px;\n            position: relative;\n        }\n        \n        .message-left {\n            background-color: #FFF0E0;\n            border-top-left-radius: 5px;\n            margin-right: auto;\n        }\n        \n        .message-right {\n            background-color: #FFE4C4;\n            border-top-right-radius: 5px;\n            margin-left: auto;\n        }\n        \n        .message-info {\n            font-size: 0.8rem;\n            color: var(--light-text);\n            margin-bottom: 5px;\n        }\n        \n        .user-chart-container {\n            height: 300px;\n            margin: 30px 0;\n        }\n        \n        .timeline {\n            position: relative;\n            padding-left: 50px;\n            margin: 40px 0;\n        }\n        \n        .timeline::before {\n            content: '';\n            position: absolute;\n            left: 20px;\n            top: 0;\n            bottom: 0;\n            width: 3px;\n            background: var(--secondary-color);\n        }\n        \n        .timeline-item {\n            position: relative;\n            margin-bottom: 30px;\n        }\n        \n        .timeline-dot {\n            position: absolute;\n            left: -50px;\n            width: 20px;\n            height: 20px;\n            border-radius: 50%;\n            background: var(--primary-color);\n            border: 3px solid white;\n            box-shadow: 0 0 0 3px var(--secondary-color);\n        }\n        \n        .timeline-content {\n            background: white;\n            padding: 20px;\n            border-radius: 10px;\n            box-shadow: 0 3px 10px rgba(0,0,0,0.1);\n        }\n        \n        .timeline-time {\n            color: var(--primary-color);\n            font-weight: bold;\n            margin-bottom: 5px;\n        }\n        \n        .timeline-user {\n            color: var(--dark-color);\n            font-weight: 600;\n        }\n        \n        .mermaid {\n            background: #FFF9F0;\n            padding: 20px;\n            border-radius: 10px;\n            margin: 20px 0;\n        }\n        \n        @media (max-width: 768px) {\n            .stat-card {\n                width: 100%;\n                margin-bottom: 15px;\n            }\n            \n            .message {\n                max-width: 85%;\n            }\n            \n            h1 {\n                font-size: 2rem;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header>\n            <h1>AI-Native产品&技术交流</h1>\n            <div class=\"subtitle\">2025年06月15日 聊天精华报告</div>\n        </header>\n        \n        <div class=\"stats-container\">\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">3</div>\n                <div class=\"stat-label\">消息总数</div>\n                <i class=\"fas fa-comments\" style=\"color: var(--primary-color); font-size: 2rem;\"></i>\n            </div>\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">2</div>\n                <div class=\"stat-label\">活跃用户</div>\n                <i class=\"fas fa-users\" style=\"color: var(--primary-color); font-size: 2rem;\"></i>\n            </div>\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">2</div>\n                <div class=\"stat-label\">有效消息</div>\n                <i class=\"fas fa-check-circle\" style=\"color: var(--primary-color); font-size: 2rem;\"></i>\n            </div>\n            <div class=\"stat-card\">\n                <div class=\"stat-value\">4'</div>\n                <div class=\"stat-label\">讨论时长</div>\n                <i class=\"fas fa-clock\" style=\"color: var(--primary-color); font-size: 2rem;\"></i>\n            </div>\n        </div>\n        \n        <div class=\"section\">\n            <h2 class=\"section-title\">核心关键词</h2>\n            <div>\n                <span class=\"keyword-tag\">投资人活动</span>\n                <span class=\"keyword-tag\">报名倒计时</span>\n                <span class=\"keyword-tag\">PAG太盟投资</span>\n                <span class=\"keyword-tag\">IDG资本</span>\n                <span class=\"keyword-tag\">高盛</span>\n                <span class=\"keyword-tag\">AI企业</span>\n            </div>\n        </div>\n        \n        <div class=\"section\">\n            <h2 class=\"section-title\">核心概念关系</h2>\n            <div class=\"mermaid\">\n                flowchart LR\n                    A[投资人活动] --> B[报名倒计时]\n                    A --> C[投资机构]\n                    C --> D[PAG太盟投资]\n                    C --> E[IDG资本]\n                    C --> F[高盛]\n                    A --> G[参与企业]\n                    G --> H[AI企业]\n                    G --> I[机器人企业]\n                    G --> J[web3企业]\n            </div>\n        </div>\n        \n        <div class=\"section\">\n            <h2 class=\"section-title\">活跃用户分析</h2>\n            <div class=\"user-chart-container\">\n                <canvas id=\"userChart\"></canvas>\n            </div>\n        </div>\n        \n        <div class=\"section\">\n            <h2 class=\"section-title\">精华对话</h2>\n            <div class=\"timeline\">\n                <div class=\"timeline-item\">\n                    <div class=\"timeline-dot\"></div>\n                    <div class=\"timeline-content\">\n                        <div class=\"timeline-time\">20:42:50</div>\n                        <div class=\"timeline-user\">Big fans of Jazz</div>\n                        <p>［转］报名倒计时6天‼️<br>\n                        【周六投资人活动已报名60+】<br>\n                        参与部分投资人名单：PAG太盟投资、软银中国、金浦投资、IDG资本、高盛、兆龙互连、盈确控股、晨熹资本、百联商投、拙朴投资、Saintander Partners（海外）、驼峰资本等等、晶科战投、仓廪资本、浩悦资本、星海控股/华懋科技CVC 、蓝图创投、鹏安基金、民生证券投行部等等。涵盖美元资本、产业资本、上市公司CVC、以及人民币基金；以及AI、机器人、web3相关企业创始人。</p>\n                        <p>位置有限了，欲报名从速！</p>\n                    </div>\n                </div>\n                <div class=\"timeline-item\">\n                    <div class=\"timeline-dot\"></div>\n                    <div class=\"timeline-content\">\n                        <div class=\"timeline-time\">20:46:08</div>\n                        <div class=\"timeline-user\">Peng</div>\n                        <p>私信你了</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"section\">\n            <h2 class=\"section-title\">消息时间分布</h2>\n            <div class=\"user-chart-container\">\n                <canvas id=\"timeChart\"></canvas>\n            </div>\n        </div>\n    </div>\n\n    <script>\n        // 用户消息统计图表\n        const userCtx = document.getElementById('userChart').getContext('2d');\n        const userChart = new Chart(userCtx, {\n            type: 'doughnut',\n            data: {\n                labels: ['Big fans of Jazz', 'Peng'],\n                datasets: [{\n                    data: [1, 1],\n                    backgroundColor: [\n                        'rgba(255, 154, 60, 0.8)',\n                        'rgba(255, 201, 60, 0.8)'\n                    ],\n                    borderColor: [\n                        'rgba(255, 154, 60, 1)',\n                        'rgba(255, 201, 60, 1)'\n                    ],\n                    borderWidth: 1\n                }]\n            },\n            options: {\n                responsive: true,\n                plugins: {\n                    legend: {\n                        position: 'top',\n                    },\n                    title: {\n                        display: true,\n                        text: '用户消息分布',\n                        font: {\n                            size: 16\n                        }\n                    }\n                }\n            }\n        });\n        \n        // 时间分布图表\n        const timeCtx = document.getElementById('timeChart').getContext('2d');\n        const timeChart = new Chart(timeCtx, {\n            type: 'line',\n            data: {\n                labels: ['20:42', '20:43', '20:44', '20:45', '20:46'],\n                datasets: [{\n                    label: '消息数量',\n                    data: [0, 1, 0, 0, 1],\n                    fill: false,\n                    backgroundColor: 'rgba(255, 106, 106, 0.8)',\n                    borderColor: 'rgba(255, 106, 106, 1)',\n                    tension: 0.1\n                }]\n            },\n            options: {\n                responsive: true,\n                scales: {\n                    y: {\n                        beginAtZero: true,\n                        ticks: {\n                            stepSize: 1\n                        }\n                    }\n                },\n                plugins: {\n                    title: {\n                        display: true,\n                        text: '消息时间分布',\n                        font: {\n                            size: 16\n                        }\n                    }\n                }\n            }\n        });\n        \n        // 初始化Mermaid\n        mermaid.initialize({\n            startOnLoad: true,\n            theme: 'default',\n            themeVariables: {\n                primaryColor: '#FFE4C4',\n                primaryBorderColor: '#FF9A3C',\n                primaryTextColor: '#5C3D2E',\n                lineColor: '#FF9A3C'\n            }\n        });\n    </script>\n</body>\n</html>", "savedAt": "2025-06-16T11:40:55.727Z"}