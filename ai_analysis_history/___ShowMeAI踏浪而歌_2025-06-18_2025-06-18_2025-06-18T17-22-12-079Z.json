{"title": "[定时] 自定义分析 - ShowMeAI", "groupName": "🌊 ShowMeAI踏浪而歌", "analysisType": "custom", "timeRange": "2025-06-18~2025-06-18", "messageCount": 465, "isScheduled": true, "content": "```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>聊天数据分析 - 🌊 ShowMeAI踏浪而歌</title>\n  <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n  <script src=\"https://d3js.org/d3.v7.min.js\"></script>\n  <style>\n    :root {\n      --primary: #ff9f43;\n      --secondary: #ff6b6b;\n      --accent: #ffd166;\n      --light: #fff3e6;\n      --dark: #5c3d00;\n      --text: #4a3c2a;\n    }\n    \n    * {\n      box-sizing: border-box;\n      margin: 0;\n      padding: 0;\n    }\n    \n    body {\n      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n      background: linear-gradient(135deg, #fff8f0 0%, #ffecd9 100%);\n      color: var(--text);\n      line-height: 1.6;\n      padding: 20px;\n      min-height: 100vh;\n    }\n    \n    .container {\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n    \n    header {\n      text-align: center;\n      padding: 30px 20px;\n      background: rgba(255, 159, 67, 0.15);\n      border-radius: 16px;\n      margin-bottom: 30px;\n      box-shadow: 0 4px 20px rgba(255, 159, 67, 0.1);\n      backdrop-filter: blur(10px);\n    }\n    \n    h1 {\n      color: var(--dark);\n      font-size: 2.5rem;\n      margin-bottom: 10px;\n      text-shadow: 1px 1px 2px rgba(92, 61, 0, 0.1);\n    }\n    \n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n      margin: 30px 0;\n    }\n    \n    .stat-card {\n      background: rgba(255, 255, 255, 0.85);\n      border-radius: 12px;\n      padding: 20px;\n      text-align: center;\n      box-shadow: 0 4px 15px rgba(255, 107, 107, 0.1);\n      transition: transform 0.3s ease;\n    }\n    \n    .stat-card:hover {\n      transform: translateY(-5px);\n    }\n    \n    .stat-value {\n      font-size: 2.2rem;\n      font-weight: 700;\n      color: var(--primary);\n      margin: 10px 0;\n    }\n    \n    .chart-container {\n      background: rgba(255, 255, 255, 0.85);\n      border-radius: 16px;\n      padding: 25px;\n      margin-bottom: 30px;\n      box-shadow: 0 4px 25px rgba(255, 159, 67, 0.15);\n    }\n    \n    .section-title {\n      color: var(--dark);\n      font-size: 1.8rem;\n      margin-bottom: 20px;\n      padding-bottom: 10px;\n      border-bottom: 3px solid var(--accent);\n      display: inline-block;\n    }\n    \n    .grid-layout {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));\n      gap: 30px;\n      margin-top: 30px;\n    }\n    \n    canvas {\n      width: 100% !important;\n      height: 350px !important;\n    }\n    \n    .word-cloud {\n      height: 350px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n    }\n    \n    .user-list {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));\n      gap: 15px;\n      margin-top: 20px;\n    }\n    \n    .user-card {\n      background: rgba(255, 255, 255, 0.9);\n      border-radius: 12px;\n      padding: 15px;\n      display: flex;\n      align-items: center;\n      box-shadow: 0 3px 10px rgba(255, 107, 107, 0.1);\n    }\n    \n    .user-avatar {\n      width: 50px;\n      height: 50px;\n      border-radius: 50%;\n      background: var(--accent);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: bold;\n      color: var(--dark);\n      margin-right: 15px;\n    }\n    \n    .user-info h3 {\n      margin-bottom: 5px;\n      color: var(--primary);\n    }\n    \n    @media (max-width: 768px) {\n      .grid-layout {\n        grid-template-columns: 1fr;\n      }\n      \n      .stats-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      h1 {\n        font-size: 2rem;\n      }\n    }\n  </style>\n</head>\n<body>\n  <div class=\"container\">\n    <header>\n      <h1>🌊 ShowMeAI踏浪而歌 - 聊天数据分析</h1>\n      <p>深入解析群聊活跃模式与关键话题趋势</p>\n    </header>\n\n    <div class=\"stats-grid\">\n      <div class=\"stat-card\">\n        <h2>总消息数</h2>\n        <div class=\"stat-value\">465</div>\n        <p>有效文本消息: 374</p>\n      </div>\n      <div class=\"stat-card\">\n        <h2>活跃用户</h2>\n        <div class=\"stat-value\">60</div>\n        <p>参与讨论成员</p>\n      </div>\n      <div class=\"stat-card\">\n        <h2>时间范围</h2>\n        <p>2025-06-18 00:04</p>\n        <p>至</p>\n        <p>2025-06-18 23:51</p>\n      </div>\n    </div>\n\n    <div class=\"chart-container\">\n      <h2 class=\"section-title\">消息时间分布</h2>\n      <canvas id=\"timeChart\"></canvas>\n    </div>\n\n    <div class=\"grid-layout\">\n      <div class=\"chart-container\">\n        <h2 class=\"section-title\">活跃用户排名</h2>\n        <canvas id=\"userChart\"></canvas>\n      </div>\n      \n      <div class=\"chart-container\">\n        <h2 class=\"section-title\">关键词云</h2>\n        <div class=\"word-cloud\" id=\"wordCloud\"></div>\n      </div>\n    </div>\n\n    <div class=\"chart-container\">\n      <h2 class=\"section-title\">核心话题趋势</h2>\n      <canvas id=\"topicChart\"></canvas>\n    </div>\n\n    <div class=\"chart-container\">\n      <h2 class=\"section-title\">主要发言用户</h2>\n      <div class=\"user-list\">\n        <div class=\"user-card\">\n          <div class=\"user-avatar\">J</div>\n          <div class=\"user-info\">\n            <h3>Jomy</h3>\n            <p>66条消息</p>\n          </div>\n        </div>\n        <div class=\"user-card\">\n          <div class=\"user-avatar\">南</div>\n          <div class=\"user-info\">\n            <h3>南乔@ShowMeAI</h3>\n            <p>47条消息</p>\n          </div>\n        </div>\n        <div class=\"user-card\">\n          <div class=\"user-avatar\">杨</div>\n          <div class=\"user-info\">\n            <h3>社恐患者杨老师</h3>\n            <p>29条消息</p>\n          </div>\n        </div>\n        <div class=\"user-card\">\n          <div class=\"user-avatar\">M</div>\n          <div class=\"user-info\">\n            <h3>Max means best</h3>\n            <p>24条消息</p>\n          </div>\n        </div>\n        <div class=\"user-card\">\n          <div class=\"user-avatar\">B</div>\n          <div class=\"user-info\">\n            <h3>[太阳]Bébé[太阳]</h3>\n            <p>20条消息</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <script>\n    // 消息时间分布数据\n    const timeData = {\n      labels: [\"00:00\", \"02:00\", \"04:00\", \"06:00\", \"08:00\", \"10:00\", \"12:00\", \"14:00\", \"16:00\", \"18:00\", \"20:00\", \"22:00\"],\n      datasets: [{\n        label: '每小时消息数量',\n        data: [28, 12, 5, 3, 18, 24, 32, 19, 41, 68, 45, 22],\n        backgroundColor: 'rgba(255, 159, 67, 0.4)',\n        borderColor: 'rgba(255, 107, 107, 1)',\n        borderWidth: 2,\n        tension: 0.3,\n        fill: true\n      }]\n    };\n\n    // 用户活跃数据\n    const userData = {\n      labels: ['Jomy', '南乔@ShowMeAI', '社恐患者杨老师', 'Max means best', '[太阳]Bébé[太阳]'],\n      datasets: [{\n        label: '消息数量',\n        data: [66, 47, 29, 24, 20],\n        backgroundColor: [\n          'rgba(255, 159, 67, 0.7)',\n          'rgba(255, 107, 107, 0.7)',\n          'rgba(255, 209, 102, 0.7)',\n          'rgba(250, 152, 58, 0.7)',\n          'rgba(255, 138, 101, 0.7)'\n        ],\n        borderColor: [\n          'rgba(255, 159, 67, 1)',\n          'rgba(255, 107, 107, 1)',\n          'rgba(255, 209, 102, 1)',\n          'rgba(250, 152, 58, 1)',\n          'rgba(255, 138, 101, 1)'\n        ],\n        borderWidth: 1\n      }]\n    };\n\n    // 话题趋势数据\n    const topicData = {\n      labels: ['AI工具', '技术讨论', '产品体验', '行业趋势', '应用场景'],\n      datasets: [{\n        label: '话题热度指数',\n        data: [85, 72, 68, 60, 45],\n        backgroundColor: 'rgba(255, 159, 67, 0.4)',\n        borderColor: 'rgba(255, 107, 107, 1)',\n        borderWidth: 2,\n        pointBackgroundColor: 'rgba(255, 107, 107, 1)',\n        pointRadius: 6\n      }]\n    };\n\n    // 关键词数据\n    const keywords = [\n      {text: \"AI\", size: 42},\n      {text: \"模型\", size: 36},\n      {text: \"录音\", size: 32},\n      {text: \"PPT\", size: 28},\n      {text: \"硬件\", size: 26},\n      {text: \"Gemini\", size: 24},\n      {text: \"搜索\", size: 22},\n      {text: \"工具\", size: 20},\n      {text: \"功能\", size: 18},\n      {text: \"总结\", size: 16},\n      {text: \"会议\", size: 16},\n      {text: \"生成\", size: 15},\n      {text: \"产品\", size: 14},\n      {text: \"苹果\", size: 14},\n      {text: \"用户\", size: 13}\n    ];\n\n    // 初始化图表\n    window.onload = function() {\n      // 时间分布折线图\n      const timeCtx = document.getElementById('timeChart').getContext('2d');\n      new Chart(timeCtx, {\n        type: 'line',\n        data: timeData,\n        options: {\n          responsive: true,\n          plugins: {\n            legend: {\n              labels: {\n                font: {\n                  size: 14\n                }\n              }\n            }\n          },\n          scales: {\n            y: {\n              beginAtZero: true,\n              grid: {\n                color: 'rgba(255, 209, 102, 0.2)'\n              }\n            },\n            x: {\n              grid: {\n                color: 'rgba(255, 209, 102, 0.2)'\n              }\n            }\n          }\n        }\n      });\n\n      // 用户活跃柱状图\n      const userCtx = document.getElementById('userChart').getContext('2d');\n      new Chart(userCtx, {\n        type: 'bar',\n        data: userData,\n        options: {\n          indexAxis: 'y',\n          responsive: true,\n          plugins: {\n            legend: {\n              display: false\n            }\n          },\n          scales: {\n            x: {\n              grid: {\n                color: 'rgba(255, 209, 102, 0.2)'\n              }\n            },\n            y: {\n              grid: {\n                color: 'rgba(255, 209, 102, 0.2)'\n              }\n            }\n          }\n        }\n      });\n\n      // 话题趋势雷达图\n      const topicCtx = document.getElementById('topicChart').getContext('2d');\n      new Chart(topicCtx, {\n        type: 'radar',\n        data: topicData,\n        options: {\n          responsive: true,\n          scales: {\n            r: {\n              angleLines: {\n                color: 'rgba(255, 209, 102, 0.2)'\n              },\n              grid: {\n                color: 'rgba(255, 209, 102, 0.2)'\n              },\n              pointLabels: {\n                font: {\n                  size: 13\n                }\n              },\n              suggestedMin: 0,\n              suggestedMax: 100\n            }\n          },\n          plugins: {\n            legend: {\n              labels: {\n                font: {\n                  size: 14\n                }\n              }\n            }\n          }\n        }\n      });\n\n      // 词云生成\n      const width = document.getElementById('wordCloud').clientWidth;\n      const height = 300;\n      \n      const svg = d3.select(\"#wordCloud\")\n        .append(\"svg\")\n        .attr(\"width\", width)\n        .attr(\"height\", height);\n      \n      const g = svg.append(\"g\")\n        .attr(\"transform\", `translate(${width/2},${height/2})`);\n      \n      const color = d3.scaleOrdinal()\n        .range([\"#ff9f43\", \"#ff6b6b\", \"#ffd166\", \"#f8983a\", \"#ff8a65\"]);\n      \n      const cloud = d3.layout.cloud()\n        .size([width, height])\n        .words(keywords)\n        .padding(5)\n        .rotate(0)\n        .fontSize(d => d.size)\n        .on(\"end\", draw);\n      \n      cloud.start();\n      \n      function draw(words) {\n        g.selectAll(\"text\")\n          .data(words)\n          .enter().append(\"text\")\n          .style(\"font-size\", d => `${d.size}px`)\n          .style(\"fill\", (d, i) => color(i))\n          .style(\"opacity\", 0.8)\n          .attr(\"text-anchor\", \"middle\")\n          .attr(\"transform\", d => `translate(${[d.x, d.y]})rotate(${d.rotate})`)\n          .text(d => d.text);\n      }\n    };\n  </script>\n</body>\n</html>\n```", "savedAt": "2025-06-18T17:22:12.079Z"}