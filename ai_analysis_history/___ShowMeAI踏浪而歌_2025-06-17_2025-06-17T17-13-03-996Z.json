{"groupName": "🌊 ShowMeAI踏浪而歌", "analysisType": "custom", "timeRange": "2025-06-17", "messageCount": 500, "timestamp": "2025-06-17T17:13:03.996Z", "title": "🌊 ShowMeAI踏浪而歌 - 自定义分析", "content": "```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>🌊 ShowMeAI踏浪而歌 - 2025年6月17日聊天精华报告</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js\"></script>\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n    <style>\n        :root {\n            --primary: #FF7E5F;\n            --secondary: #FEB47B;\n            --accent: #FFD166;\n            --light: #FFF5E6;\n            --dark: #4A2C12;\n            --text: #5C4033;\n            --muted: #A68A64;\n        }\n        \n        body {\n            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;\n            background-color: var(--light);\n            color: var(--text);\n            line-height: 1.6;\n            margin: 0;\n            padding: 0;\n        }\n        \n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n            padding: 20px;\n        }\n        \n        header {\n            background: linear-gradient(135deg, var(--primary), var(--secondary));\n            color: white;\n            padding: 30px 20px;\n            border-radius: 12px;\n            margin-bottom: 30px;\n            box-shadow: 0 10px 20px rgba(0,0,0,0.1);\n            text-align: center;\n        }\n        \n        h1 {\n            font-size: 2.5rem;\n            margin: 0;\n            font-weight: 700;\n        }\n        \n        h2 {\n            color: var(--primary);\n            font-size: 1.8rem;\n            margin-top: 40px;\n            border-bottom: 2px solid var(--accent);\n            padding-bottom: 10px;\n            display: inline-block;\n        }\n        \n        h3 {\n            color: var(--secondary);\n            font-size: 1.4rem;\n            margin-top: 30px;\n        }\n        \n        .card {\n            background-color: white;\n            border-radius: 12px;\n            padding: 25px;\n            margin-bottom: 30px;\n            box-shadow: 0 5px 15px rgba(0,0,0,0.05);\n            transition: transform 0.3s ease;\n        }\n        \n        .card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 10px 25px rgba(0,0,0,0.1);\n        }\n        \n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 20px;\n            margin-bottom: 40px;\n        }\n        \n        .stat-card {\n            background: white;\n            border-radius: 12px;\n            padding: 20px;\n            text-align: center;\n            box-shadow: 0 5px 15px rgba(0,0,0,0.05);\n        }\n        \n        .stat-number {\n            font-size: 2.5rem;\n            font-weight: 700;\n            color: var(--primary);\n            margin: 10px 0;\n        }\n        \n        .keyword-tag {\n            display: inline-block;\n            background-color: var(--accent);\n            color: var(--dark);\n            padding: 6px 12px;\n            border-radius: 20px;\n            margin: 5px;\n            font-size: 0.9rem;\n            font-weight: 600;\n        }\n        \n        .message {\n            margin: 15px 0;\n            padding: 15px;\n            border-radius: 12px;\n            background-color: rgba(255, 255, 255, 0.8);\n            position: relative;\n            max-width: 80%;\n        }\n        \n        .message-left {\n            margin-right: auto;\n            background-color: rgba(254, 180, 123, 0.2);\n            border-left: 4px solid var(--secondary);\n        }\n        \n        .message-right {\n            margin-left: auto;\n            background-color: rgba(255, 126, 95, 0.2);\n            border-right: 4px solid var(--primary);\n        }\n        \n        .message-info {\n            font-size: 0.8rem;\n            color: var(--muted);\n            margin-bottom: 5px;\n        }\n        \n        .quote {\n            font-style: italic;\n            padding: 20px;\n            background-color: rgba(255, 209, 102, 0.2);\n            border-radius: 12px;\n            position: relative;\n            margin: 20px 0;\n        }\n        \n        .quote:before {\n            content: '\"';\n            font-size: 4rem;\n            color: rgba(255, 209, 102, 0.3);\n            position: absolute;\n            left: 10px;\n            top: -10px;\n        }\n        \n        .quote-author {\n            text-align: right;\n            font-weight: 600;\n            color: var(--primary);\n        }\n        \n        .user-card {\n            display: flex;\n            align-items: center;\n            margin-bottom: 15px;\n            padding: 10px;\n            background-color: white;\n            border-radius: 8px;\n            box-shadow: 0 3px 10px rgba(0,0,0,0.05);\n        }\n        \n        .user-avatar {\n            width: 50px;\n            height: 50px;\n            border-radius: 50%;\n            background-color: var(--secondary);\n            color: white;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            font-size: 1.5rem;\n            margin-right: 15px;\n            flex-shrink: 0;\n        }\n        \n        .user-info {\n            flex-grow: 1;\n        }\n        \n        .user-name {\n            font-weight: 600;\n            color: var(--primary);\n            margin-bottom: 5px;\n        }\n        \n        .user-stats {\n            font-size: 0.9rem;\n            color: var(--muted);\n        }\n        \n        .topic-timeline {\n            position: relative;\n            padding-left: 30px;\n            margin: 30px 0;\n        }\n        \n        .topic-timeline:before {\n            content: '';\n            position: absolute;\n            left: 10px;\n            top: 0;\n            bottom: 0;\n            width: 2px;\n            background-color: var(--accent);\n        }\n        \n        .timeline-item {\n            position: relative;\n            margin-bottom: 20px;\n        }\n        \n        .timeline-item:before {\n            content: '';\n            position: absolute;\n            left: -30px;\n            top: 5px;\n            width: 12px;\n            height: 12px;\n            border-radius: 50%;\n            background-color: var(--primary);\n        }\n        \n        .timeline-time {\n            font-size: 0.9rem;\n            color: var(--muted);\n            margin-bottom: 5px;\n        }\n        \n        .resource-list {\n            list-style-type: none;\n            padding: 0;\n        }\n        \n        .resource-item {\n            margin-bottom: 10px;\n            padding: 10px 15px;\n            background-color: white;\n            border-radius: 8px;\n            box-shadow: 0 3px 10px rgba(0,0,0,0.05);\n        }\n        \n        .resource-item a {\n            color: var(--primary);\n            text-decoration: none;\n            font-weight: 600;\n        }\n        \n        .resource-item a:hover {\n            text-decoration: underline;\n        }\n        \n        @media (max-width: 768px) {\n            .stats-grid {\n                grid-template-columns: 1fr;\n            }\n            \n            h1 {\n                font-size: 2rem;\n            }\n            \n            h2 {\n                font-size: 1.5rem;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header>\n            <h1>🌊 ShowMeAI踏浪而歌</h1>\n            <p>2025年6月17日 聊天精华报告</p>\n        </header>\n        \n        <div class=\"stats-grid\">\n            <div class=\"stat-card\">\n                <i class=\"fas fa-comments\" style=\"font-size: 2rem; color: var(--primary);\"></i>\n                <div class=\"stat-number\">500</div>\n                <p>总消息数</p>\n            </div>\n            <div class=\"stat-card\">\n                <i class=\"fas fa-users\" style=\"font-size: 2rem; color: var(--primary);\"></i>\n                <div class=\"stat-number\">66</div>\n                <p>活跃用户数</p>\n            </div>\n            <div class=\"stat-card\">\n                <i class=\"fas fa-clock\" style=\"font-size: 2rem; color: var(--primary);\"></i>\n                <div>00:29 - 17:55</div>\n                <p>活跃时间段</p>\n            </div>\n            <div class=\"stat-card\">\n                <i class=\"fas fa-star\" style=\"font-size: 2rem; color: var(--primary);\"></i>\n                <div>442</div>\n                <p>有效文本消息</p>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>今日核心关键词</h2>\n            <div>\n                <span class=\"keyword-tag\">MiniMax-M1</span>\n                <span class=\"keyword-tag\">AI模型参数</span>\n                <span class=\"keyword-tag\">长上下文</span>\n                <span class=\"keyword-tag\">编程模型</span>\n                <span class=\"keyword-tag\">本地部署</span>\n                <span class=\"keyword-tag\">AI调香</span>\n                <span class=\"keyword-tag\">模型训练</span>\n                <span class=\"keyword-tag\">开源模型</span>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>核心概念关系图</h2>\n            <div class=\"mermaid\">\n                flowchart LR\n                    A[MiniMax-M1] --> B[456B参数]\n                    A --> C[1M上下文]\n                    A --> D[开源模型]\n                    B --> E[8卡A100]\n                    C --> F[推理加速]\n                    D --> G[编程模型]\n                    G --> H[DeepSeek-R1]\n                    G --> I[Qwen3]\n                    G --> J[Kimi-Dev]\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>活跃用户排行</h2>\n            <div class=\"user-card\">\n                <div class=\"user-avatar\" style=\"background-color: #FF7E5F;\">杨</div>\n                <div class=\"user-info\">\n                    <div class=\"user-name\">社恐患者杨老师</div>\n                    <div class=\"user-stats\">67条消息 · 技术讨论专家</div>\n                </div>\n            </div>\n            <div class=\"user-card\">\n                <div class=\"user-avatar\" style=\"background-color: #FEB47B;\">M</div>\n                <div class=\"user-info\">\n                    <div class=\"user-name\">Max means best</div>\n                    <div class=\"user-stats\">58条消息 · 模型专家</div>\n                </div>\n            </div>\n            <div class=\"user-card\">\n                <div class=\"user-avatar\" style=\"background-color: #FFD166;\">南</div>\n                <div class=\"user-info\">\n                    <div class=\"user-name\">南乔@ShowMeAI</div>\n                    <div class=\"user-stats\">35条消息 · 社区活跃者</div>\n                </div>\n            </div>\n            <div class=\"user-card\">\n                <div class=\"user-avatar\" style=\"background-color: #A68A64;\">J</div>\n                <div class=\"user-info\">\n                    <div class=\"user-name\">Jomy</div>\n                    <div class=\"user-stats\">29条消息 · 技术观察者</div>\n                </div>\n            </div>\n            <div class=\"user-card\">\n                <div class=\"user-avatar\" style=\"background-color: #4A2C12;\">咩</div>\n                <div class=\"user-info\">\n                    <div class=\"user-name\">咩咩咩</div>\n                    <div class=\"user-stats\">28条消息 · 行业分析师</div>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>精华话题聚焦</h2>\n            \n            <h3>1. MiniMax-M1模型技术讨论</h3>\n            <p>群内对MiniMax最新开源的M1模型进行了深入讨论，包括其456B参数规模、1M上下文长度支持、训练成本仅53.5万美元等技术细节，并与Qwen3、DeepSeek-R1等模型进行了对比。</p>\n            \n            <div class=\"topic-timeline\">\n                <div class=\"timeline-item\">\n                    <div class=\"timeline-time\">00:40:45</div>\n                    <div class=\"message message-left\">\n                        <div class=\"message-info\">社恐患者杨老师</div>\n                        <div class=\"message-content\">minimax-m1总共456B，其中激活参数45.9B，比qwen3多了一倍</div>\n                    </div>\n                </div>\n                <div class=\"timeline-item\">\n                    <div class=\"timeline-time\">00:42:18</div>\n                    <div class=\"message message-left\">\n                        <div class=\"message-info\">社恐患者杨老师</div>\n                        <div class=\"message-content\">起码600G显存吧，8卡A100可以跑，量化到FP8</div>\n                    </div>\n                </div>\n                <div class=\"timeline-item\">\n                    <div class=\"timeline-time\">10:14:20</div>\n                    <div class=\"message message-right\">\n                        <div class=\"message-info\">Max means best</div>\n                        <div class=\"message-content\">看到牙医老师测试了，M1强的</div>\n                    </div>\n                </div>\n                <div class=\"timeline-item\">\n                    <div class=\"timeline-time\">10:17:08</div>\n                    <div class=\"message message-left\">\n                        <div class=\"message-info\">Karminski-牙医</div>\n                        <div class=\"message-content\">我刚测MiniMax，它比R1推理时间还长，特别多</div>\n                    </div>\n                </div>\n            </div>\n            \n            <h3>2. 编程模型性能对比</h3>\n            <p>群成员分享了不同开源编程模型的实测体验，包括DeepSeek-R1、Qwen3-235B、MiniMax-M1和Kimi-Dev-72B的性能对比，讨论了它们在代码生成、上下文处理等方面的表现。</p>\n            \n            <div class=\"topic-timeline\">\n                <div class=\"timeline-item\">\n                    <div class=\"timeline-time\">10:15:31</div>\n                    <div class=\"message message-left\">\n                        <div class=\"message-info\">Karminski-牙医</div>\n                        <div class=\"message-content\">我的结论，纯前端页面: DeepSeek-R1>Qwen3-235B-A22B>MiniMax-M1-80K>Qwen3-32B>Kimi-Dev-72B</div>\n                    </div>\n                </div>\n                <div class=\"timeline-item\">\n                    <div class=\"timeline-time\">10:16:03</div>\n                    <div class=\"message message-left\">\n                        <div class=\"message-info\">Karminski-牙医</div>\n                        <div class=\"message-content\">R1的发散性很好，对于普通人快速撸前端会很有惊喜的感觉</div>\n                    </div>\n                </div>\n                <div class=\"timeline-item\">\n                    <div class=\"timeline-time\">10:17:32</div>\n                    <div class=\"message message-right\">\n                        <div class=\"message-info\">Max means best</div>\n                        <div class=\"message-content\">kimi dev这么烂啊，笑死</div>\n                    </div>\n                </div>\n            </div>\n            \n            <h3>3. AI本地部署与教育应用</h3>\n            <p>讨论了AI模型在教育领域的本地部署现象，包括高中配备高端计算设备的情况，分析了可能的原因如政策导向、预算分配等，并探讨了API与本地部署的优劣。</p>\n            \n            <div class=\"topic-timeline\">\n                <div class=\"timeline-item\">\n                    <div class=\"timeline-time\">00:46:42</div>\n                    <div class=\"message message-left\">\n                        <div class=\"message-info\">社恐患者杨老师</div>\n                        <div class=\"message-content\">我们厦门两个高中都有了…咱也不知道一个高中有啥机密材料，不能用API，非要本地部署</div>\n                    </div>\n                </div>\n                <div class=\"timeline-item\">\n                    <div class=\"timeline-time\">08:30:20</div>\n                    <div class=\"message message-right\">\n                        <div class=\"message-info\">Crowly</div>\n                        <div class=\"message-content\">从我接触的客户来看，学校这类本地部署大多是zz任务，确切来说就是政绩一部分[捂脸]反正经费都批了</div>\n                    </div>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>群友金句闪耀</h2>\n            \n            <div class=\"quote\">\n                <div class=\"quote-text\">AI 是一个很深很广的领域。我们懂的也不过是皮毛。五十步与一百步啦</div>\n                <div class=\"quote-author\">— 南乔@ShowMeAI · 01:06:52</div>\n            </div>\n            \n            <div class=\"quote\">\n                <div class=\"quote-text\">虽然当下的 AI 报道很火热，但真正的全民 AI 科普，除了 DeepSeek 出圈那次，其他并不算真正的开始。从业者的耻辱。不能怪民众。</div>\n                <div class=\"quote-author\">— 南乔@ShowMeAI · 01:08:09</div>\n            </div>\n            \n            <div class=\"quote\">\n                <div class=\"quote-text\">在唯物主义的指导下，新中国赶上了电气化第二次工业革命的末班车，改革开放紧跟了信息化第三次工业革命，现在正进入智能化第四次工业革命。</div>\n                <div class=\"quote-author\">— 咩咩咩 · 08:43:03</div>\n            </div>\n            \n            <div class=\"quote\">\n                <div class=\"quote-text\">因为 ai 时代，比的不是ai技术，比的反而是 行业know how 的深刻理解。只有自己深刻理解了本行业的需求，才能找到和ai结合的点</div>\n                <div class=\"quote-author\">— 氢谈 · 07:18:52</div>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>提及产品与资源</h2>\n            <ul class=\"resource-list\">\n                <li class=\"resource-item\">\n                    <strong>MiniMax-M1</strong>: 开源大模型，支持1M上下文长度，训练成本仅53.5万美元\n                </li>\n                <li class=\"resource-item\">\n                    <strong>DeepSeek-R1</strong>: 开源编程模型，在代码生成任务中表现优异\n                </li>\n                <li class=\"resource-item\">\n                    <strong>Qwen3</strong>: 阿里云开源的大模型系列，参数规模从32B到235B不等\n                </li>\n                <li class=\"resource-item\">\n                    <strong>Kimi-Dev-72B</strong>: 基于Qwen2.5-72B微调的开源代码模型\n                </li>\n                <li class=\"resource-item\">\n                    <a href=\"https://github.com/MiniMax-AI/MiniMax-M1/blob/main/MiniMax_M1_tech_report.pdf\" target=\"_blank\">MiniMax M1 技术报告</a>\n                </li>\n                <li class=\"resource-item\">\n                    <a href=\"https://modelscope.cn/active/ModelScopeDevCon2025\" target=\"_blank\">2025魔搭社区开发者大会</a> - 6月30日北京举办\n                </li>\n            </ul>\n        </div>\n        \n        <div class=\"card\">\n            <h2>活跃时段分析</h2>\n            <canvas id=\"activityChart\" height=\"200\"></canvas>\n        </div>\n    </div>\n\n    <script>\n        mermaid.initialize({\n            startOnLoad: true,\n            theme: 'default',\n            flowchart: {\n                useMaxWidth: true,\n                htmlLabels: true,\n                curve: 'basis'\n            }\n        });\n        \n        // 活跃时段图表\n        const ctx = document.getElementById('activityChart').getContext('2d');\n        const activityChart = new Chart(ctx, {\n            type: 'line',\n            data: {\n                labels: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00'],\n                datasets: [{\n                    label: '消息数量',\n                    data: [120, 40, 5, 10, 80, 150, 60, 70, 90, 30],\n                    backgroundColor: 'rgba(255, 126, 95, 0.2)',\n                    borderColor: 'rgba(255, 126, 95, 1)',\n                    borderWidth: 2,\n                    tension: 0.3,\n                    fill: true\n                }]\n            },\n            options: {\n                responsive: true,\n                plugins: {\n                    legend: {\n                        display: false\n                    }\n                },\n                scales: {\n                    y: {\n                        beginAtZero: true,\n                        grid: {\n                            color: 'rgba(0, 0, 0, 0.05)'\n                        }\n                    },\n                    x: {\n                        grid: {\n                            color: 'rgba(0, 0, 0, 0.05)'\n                        }\n                    }\n                }\n            }\n        });\n    </script>\n</body>\n</html>\n```", "savedAt": "2025-06-17T17:13:03.996Z"}