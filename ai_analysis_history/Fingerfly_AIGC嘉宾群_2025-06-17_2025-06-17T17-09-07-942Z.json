{"groupName": "Fingerfly AIGC嘉宾群", "analysisType": "custom", "timeRange": "2025-06-17", "messageCount": 45, "timestamp": "2025-06-17T17:09:07.942Z", "title": "Fingerfly AIGC嘉宾群 - 自定义分析", "content": "```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Fingerfly AIGC嘉宾群 - 2025-06-17 聊天分析报告</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js\"></script>\n    <style>\n        :root {\n            --primary-color: #FF7E5F;\n            --secondary-color: #FFB347;\n            --accent-color: #FFD166;\n            --light-color: #FFF3E0;\n            --dark-color: #5C4033;\n            --text-color: #333333;\n            --bg-color: #FFF9F0;\n        }\n        \n        body {\n            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;\n            background-color: var(--bg-color);\n            color: var(--text-color);\n            line-height: 1.6;\n            margin: 0;\n            padding: 0;\n        }\n        \n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n            padding: 20px;\n        }\n        \n        header {\n            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n            color: white;\n            padding: 30px 20px;\n            border-radius: 10px;\n            margin-bottom: 30px;\n            box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n        }\n        \n        h1 {\n            margin: 0;\n            font-size: 2.2rem;\n            font-weight: 700;\n        }\n        \n        h2 {\n            color: var(--primary-color);\n            border-bottom: 2px solid var(--accent-color);\n            padding-bottom: 8px;\n            margin-top: 40px;\n        }\n        \n        .card {\n            background-color: white;\n            border-radius: 10px;\n            padding: 20px;\n            margin-bottom: 30px;\n            box-shadow: 0 4px 8px rgba(0,0,0,0.05);\n            transition: transform 0.3s ease, box-shadow 0.3s ease;\n        }\n        \n        .card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 8px 16px rgba(0,0,0,0.1);\n        }\n        \n        .keyword-tag {\n            display: inline-block;\n            background-color: var(--accent-color);\n            color: var(--dark-color);\n            padding: 6px 12px;\n            border-radius: 20px;\n            margin: 5px;\n            font-size: 0.9rem;\n            font-weight: 500;\n        }\n        \n        .message {\n            margin-bottom: 15px;\n            max-width: 80%;\n        }\n        \n        .message.sent {\n            margin-left: auto;\n            background-color: var(--light-color);\n            border-radius: 18px 18px 0 18px;\n            padding: 12px 16px;\n        }\n        \n        .message.received {\n            margin-right: auto;\n            background-color: white;\n            border: 1px solid #eee;\n            border-radius: 18px 18px 18px 0;\n            padding: 12px 16px;\n        }\n        \n        .message-info {\n            font-size: 0.8rem;\n            color: #777;\n            margin-bottom: 5px;\n        }\n        \n        .message-content {\n            word-wrap: break-word;\n        }\n        \n        .user-bubble {\n            background-color: var(--light-color);\n            border-radius: 10px;\n            padding: 10px 15px;\n            margin: 5px 0;\n            display: inline-block;\n            max-width: 80%;\n        }\n        \n        .quote {\n            font-style: italic;\n            color: var(--dark-color);\n            border-left: 3px solid var(--primary-color);\n            padding-left: 15px;\n            margin: 20px 0;\n        }\n        \n        .quote-author {\n            text-align: right;\n            font-weight: 500;\n            color: var(--primary-color);\n        }\n        \n        .stats-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 20px;\n            margin: 30px 0;\n        }\n        \n        .stat-item {\n            text-align: center;\n            padding: 20px;\n            background-color: white;\n            border-radius: 10px;\n            box-shadow: 0 4px 8px rgba(0,0,0,0.05);\n        }\n        \n        .stat-value {\n            font-size: 2.5rem;\n            font-weight: 700;\n            color: var(--primary-color);\n            margin: 10px 0;\n        }\n        \n        .stat-label {\n            font-size: 1rem;\n            color: #666;\n        }\n        \n        .time-chart-container {\n            height: 300px;\n            margin: 30px 0;\n        }\n        \n        .user-chart-container {\n            height: 400px;\n            margin: 30px 0;\n        }\n        \n        .mermaid {\n            background-color: white;\n            padding: 20px;\n            border-radius: 10px;\n            margin: 20px 0;\n        }\n        \n        @media (max-width: 768px) {\n            .stats-grid {\n                grid-template-columns: 1fr;\n            }\n            \n            h1 {\n                font-size: 1.8rem;\n            }\n            \n            .message {\n                max-width: 90%;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header>\n            <h1>Fingerfly AIGC嘉宾群</h1>\n            <p>2025年6月17日 聊天分析报告</p>\n        </header>\n        \n        <div class=\"stats-grid\">\n            <div class=\"stat-item\">\n                <div class=\"stat-value\">45</div>\n                <div class=\"stat-label\">消息总数</div>\n            </div>\n            <div class=\"stat-item\">\n                <div class=\"stat-value\">11</div>\n                <div class=\"stat-label\">活跃用户数</div>\n            </div>\n            <div class=\"stat-item\">\n                <div class=\"stat-value\">13h</div>\n                <div class=\"stat-label\">聊天时长</div>\n            </div>\n            <div class=\"stat-item\">\n                <div class=\"stat-value\">16</div>\n                <div class=\"stat-label\">向阳乔木发言数</div>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>核心关键词</h2>\n            <div>\n                <span class=\"keyword-tag\">视频分享</span>\n                <span class=\"keyword-tag\">LMarena榜</span>\n                <span class=\"keyword-tag\">Web开发</span>\n                <span class=\"keyword-tag\">公众号流量</span>\n                <span class=\"keyword-tag\">情绪向内容</span>\n                <span class=\"keyword-tag\">日本大学</span>\n                <span class=\"keyword-tag\">台湾腔</span>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>核心概念关系图</h2>\n            <div class=\"mermaid\">\n                flowchart LR\n                    A[视频分享] --> B[LMarena榜]\n                    B --> C{Web开发场景}\n                    C --> D[DS超过Opus 4]\n                    C --> E[国产之光]\n                    F[公众号流量] --> G[32%推荐池]\n                    G --> H[转发率高]\n                    I[情绪向内容] --> J[10w关注后]\n                    K[日本大学] --> L[中文分享]\n                    L --> M[台湾腔]\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>活跃时段分析</h2>\n            <div class=\"time-chart-container\">\n                <canvas id=\"timeChart\"></canvas>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>用户活跃度</h2>\n            <div class=\"user-chart-container\">\n                <canvas id=\"userChart\"></canvas>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>精华话题</h2>\n            \n            <h3>1. 视频分享与LMarena榜单</h3>\n            <p>向阳乔木分享了多个视频链接，并提到在LMarena榜中，Web开发场景下DS已经超过Opus 4，称赞其为\"国产之光\"。</p>\n            \n            <div class=\"message sent\">\n                <div class=\"message-info\">向阳乔木 09:21</div>\n                <div class=\"message-content\">LMarena榜中，Web开发场景，DS都超过Opus 4了，真国产之光。</div>\n            </div>\n            \n            <div class=\"message received\">\n                <div class=\"message-info\">堕落的猴子 09:23</div>\n                <div class=\"message-content\">感觉有哪些不太对劲。。。</div>\n            </div>\n            \n            <h3>2. 公众号流量分析</h3>\n            <p>向阳乔木分析了3月初到现在的文章数据，发现公众号推荐池流量占比32%左右，讨论了转发率高是因还是果的问题。</p>\n            \n            <div class=\"message sent\">\n                <div class=\"message-info\">向阳乔木 11:19</div>\n                <div class=\"message-content\">确实是，公众号推荐池流量占比32%左右，转发率高不知道是因还是果</div>\n            </div>\n            \n            <div class=\"message received\">\n                <div class=\"message-info\">歸藏 11:22</div>\n                <div class=\"message-content\">比较有获得感</div>\n            </div>\n            \n            <div class=\"message received\">\n                <div class=\"message-info\">歸藏 11:22</div>\n                <div class=\"message-content\">但情绪向的也数据好 我们不太会写</div>\n            </div>\n            \n            <h3>3. 日本大学分享经历</h3>\n            <p>群友讨论了在日本大学的分享经历，提到因为参会华人多，最终选择了中文分享而非英文。</p>\n            \n            <div class=\"message received\">\n                <div class=\"message-info\">LXfater 12:46</div>\n                <div class=\"message-content\">这是在哪里呀？</div>\n            </div>\n            \n            <div class=\"message sent\">\n                <div class=\"message-info\">向阳乔木 12:49</div>\n                <div class=\"message-content\">在日本的一个大学的教室</div>\n            </div>\n            \n            <div class=\"message sent\">\n                <div class=\"message-info\">向阳乔木 15:52</div>\n                <div class=\"message-content\">不过确实参会的华人多，中文更容易传递信息</div>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>群友金句</h2>\n            \n            <div class=\"quote\">\n                \"公众号推荐池流量占比32%左右，转发率高不知道是因还是果\"\n                <div class=\"quote-author\">— 向阳乔木</div>\n            </div>\n            \n            <div class=\"quote\">\n                \"比较有获得感\"\n                <div class=\"quote-author\">— 歸藏</div>\n            </div>\n            \n            <div class=\"quote\">\n                \"情绪在10w关注以后可以试的事情\"\n                <div class=\"quote-author\">— Leo🍊Orange AI</div>\n            </div>\n            \n            <div class=\"quote\">\n                \"笑死我了 这个台湾腔\"\n                <div class=\"quote-author\">— 向阳乔木</div>\n            </div>\n        </div>\n        \n        <div class=\"card\">\n            <h2>提及资源</h2>\n            <ul>\n                <li><a href=\"https://www.bilibili.com/video/BV1i7NYzmEaD/\" target=\"_blank\">Bilibili视频1</a></li>\n                <li><a href=\"https://www.bilibili.com/video/BV1hsNhzNEdW/\" target=\"_blank\">Bilibili视频2</a></li>\n                <li><a href=\"https://www.bilibili.com/video/BV1EDNhz7EBB/\" target=\"_blank\">Bilibili视频3</a></li>\n            </ul>\n        </div>\n    </div>\n\n    <script>\n        // 时间分布数据\n        const timeData = {\n            labels: [\"8:00\", \"9:00\", \"10:00\", \"11:00\", \"12:00\", \"13:00\", \"14:00\", \"15:00\", \"16:00\", \"17:00\", \"18:00\", \"19:00\", \"20:00\", \"21:00\"],\n            datasets: [{\n                label: '每小时消息数',\n                data: [5, 6, 0, 12, 4, 0, 0, 4, 2, 0, 0, 0, 0, 4],\n                backgroundColor: 'rgba(255, 126, 95, 0.5)',\n                borderColor: 'rgba(255, 126, 95, 1)',\n                borderWidth: 2,\n                tension: 0.3\n            }]\n        };\n        \n        // 用户活跃度数据\n        const userData = {\n            labels: ['向阳乔木', '歸藏', '姚金刚', '李福祥', 'Brad 强', '堕落的猴子', 'Leo🍊Orange AI', '汗青', 'LXfater', 'Hero', 'indigo'],\n            datasets: [{\n                label: '发言数量',\n                data: [16, 4, 2, 2, 2, 1, 1, 1, 1, 1, 1],\n                backgroundColor: [\n                    'rgba(255, 126, 95, 0.7)',\n                    'rgba(255, 179, 71, 0.7)',\n                    'rgba(255, 209, 102, 0.7)',\n                    'rgba(255, 243, 224, 0.7)',\n                    'rgba(255, 159, 64, 0.7)',\n                    'rgba(255, 99, 132, 0.7)',\n                    'rgba(54, 162, 235, 0.7)',\n                    'rgba(75, 192, 192, 0.7)',\n                    'rgba(153, 102, 255, 0.7)',\n                    'rgba(255, 159, 64, 0.7)',\n                    'rgba(199, 199, 199, 0.7)'\n                ],\n                borderColor: [\n                    'rgba(255, 126, 95, 1)',\n                    'rgba(255, 179, 71, 1)',\n                    'rgba(255, 209, 102, 1)',\n                    'rgba(255, 243, 224, 1)',\n                    'rgba(255, 159, 64, 1)',\n                    'rgba(255, 99, 132, 1)',\n                    'rgba(54, 162, 235, 1)',\n                    'rgba(75, 192, 192, 1)',\n                    'rgba(153, 102, 255, 1)',\n                    'rgba(255, 159, 64, 1)',\n                    'rgba(199, 199, 199, 1)'\n                ],\n                borderWidth: 1\n            }]\n        };\n        \n        // 初始化时间图表\n        const timeCtx = document.getElementById('timeChart').getContext('2d');\n        new Chart(timeCtx, {\n            type: 'line',\n            data: timeData,\n            options: {\n                responsive: true,\n                maintainAspectRatio: false,\n                scales: {\n                    y: {\n                        beginAtZero: true,\n                        title: {\n                            display: true,\n                            text: '消息数量'\n                        }\n                    },\n                    x: {\n                        title: {\n                            display: true,\n                            text: '时间'\n                        }\n                    }\n                }\n            }\n        });\n        \n        // 初始化用户图表\n        const userCtx = document.getElementById('userChart').getContext('2d');\n        new Chart(userCtx, {\n            type: 'bar',\n            data: userData,\n            options: {\n                responsive: true,\n                maintainAspectRatio: false,\n                scales: {\n                    y: {\n                        beginAtZero: true,\n                        title: {\n                            display: true,\n                            text: '发言数量'\n                        }\n                    },\n                    x: {\n                        title: {\n                            display: true,\n                            text: '用户'\n                        }\n                    }\n                }\n            }\n        });\n        \n        // 初始化Mermaid图表\n        mermaid.initialize({\n            startOnLoad: true,\n            theme: 'default',\n            themeVariables: {\n                primaryColor: '#FFF3E0',\n                nodeBorder: '#FF7E5F',\n                lineColor: '#FFB347',\n                textColor: '#5C4033'\n            }\n        });\n    </script>\n</body>\n</html>\n```", "savedAt": "2025-06-17T17:09:07.942Z"}