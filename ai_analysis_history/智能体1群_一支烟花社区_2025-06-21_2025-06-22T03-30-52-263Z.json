{"groupName": "智能体1群|一支烟花社区", "analysisType": "science", "timeRange": "2025-06-21", "messageCount": 421, "timestamp": "2025-06-22T03:30:52.263Z", "title": "智能体1群|一支烟花社区 - 科学学习分析", "content": "```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>智能体1群聊天数据分析</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/wordcloud@1.2.2/src/wordcloud2.min.js\"></script>\n    <style>\n        :root {\n            --primary: #FF7F50;\n            --secondary: #FFA07A;\n            --accent: #FF6347;\n            --background: #FFF5E6;\n            --text: #5A3921;\n            --card: #FFE4C4;\n        }\n        \n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n        \n        body {\n            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n            background-color: var(--background);\n            color: var(--text);\n            line-height: 1.6;\n            padding: 20px;\n        }\n        \n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n        }\n        \n        header {\n            text-align: center;\n            margin-bottom: 30px;\n            padding: 20px;\n            background: linear-gradient(135deg, var(--primary), var(--secondary));\n            border-radius: 15px;\n            box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n            color: white;\n        }\n        \n        h1 {\n            font-size: 2.5rem;\n            margin-bottom: 10px;\n        }\n        \n        .stats-container {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n            gap: 20px;\n            margin-bottom: 30px;\n        }\n        \n        .stat-card {\n            background-color: var(--card);\n            border-radius: 10px;\n            padding: 20px;\n            text-align: center;\n            box-shadow: 0 3px 10px rgba(0,0,0,0.08);\n        }\n        \n        .stat-card h3 {\n            color: var(--accent);\n            margin-bottom: 10px;\n        }\n        \n        .stat-value {\n            font-size: 2rem;\n            font-weight: bold;\n            color: var(--primary);\n        }\n        \n        .charts-container {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));\n            gap: 25px;\n            margin-bottom: 30px;\n        }\n        \n        .chart-card {\n            background-color: white;\n            border-radius: 15px;\n            padding: 20px;\n            box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n        }\n        \n        .chart-title {\n            text-align: center;\n            margin-bottom: 15px;\n            color: var(--accent);\n            font-size: 1.3rem;\n        }\n        \n        .chart-container {\n            position: relative;\n            height: 300px;\n            width: 100%;\n        }\n        \n        #wordcloud {\n            width: 100%;\n            height: 400px;\n            background-color: white;\n            border-radius: 15px;\n            margin: 30px auto;\n            box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n            display: flex;\n            justify-content: center;\n            align-items: center;\n        }\n        \n        @media (max-width: 768px) {\n            .charts-container {\n                grid-template-columns: 1fr;\n            }\n            \n            .chart-card {\n                padding: 15px;\n            }\n            \n            .chart-container {\n                height: 250px;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header>\n            <h1>智能体1群 | 一支烟花社区</h1>\n            <p>聊天数据分析报告</p>\n        </header>\n        \n        <div class=\"stats-container\">\n            <div class=\"stat-card\">\n                <h3>消息总数</h3>\n                <div class=\"stat-value\">421</div>\n                <p>有效文本消息: 338</p>\n            </div>\n            <div class=\"stat-card\">\n                <h3>活跃用户数</h3>\n                <div class=\"stat-value\">60</div>\n                <p>参与讨论成员</p>\n            </div>\n            <div class=\"stat-card\">\n                <h3>时间范围</h3>\n                <p>2025-06-21 06:44</p>\n                <p>至 2025-06-21 23:46</p>\n                <div class=\"stat-value\">17小时</div>\n            </div>\n        </div>\n        \n        <div class=\"charts-container\">\n            <div class=\"chart-card\">\n                <div class=\"chart-title\">活跃用户消息量排名</div>\n                <div class=\"chart-container\">\n                    <canvas id=\"userChart\"></canvas>\n                </div>\n            </div>\n            \n            <div class=\"chart-card\">\n                <div class=\"chart-title\">消息时间分布（按小时）</div>\n                <div class=\"chart-container\">\n                    <canvas id=\"timeChart\"></canvas>\n                </div>\n            </div>\n            \n            <div class=\"chart-card\">\n                <div class=\"chart-title\">消息类型分布</div>\n                <div class=\"chart-container\">\n                    <canvas id=\"typeChart\"></canvas>\n                </div>\n            </div>\n            \n            <div class=\"chart-card\">\n                <div class=\"chart-title\">讨论主题分布</div>\n                <div class=\"chart-container\">\n                    <canvas id=\"topicChart\"></canvas>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"chart-title\">聊天关键词词云</div>\n        <div id=\"wordcloud\"></div>\n    </div>\n\n    <script>\n        // 用户消息数据\n        const userData = {\n            labels: ['不辣的皮皮', 'HEXIN', '许光耀', '启曜@AI软硬件', '年轮'],\n            datasets: [{\n                label: '消息数量',\n                data: [84, 51, 29, 15, 12],\n                backgroundColor: [\n                    '#FF7F50', '#FFA07A', '#FF6347', '#FF4500', '#FF8C00'\n                ],\n                borderWidth: 1\n            }]\n        };\n\n        // 时间分布数据\n        const timeData = {\n            labels: ['06-07', '08-09', '10-11', '12-13', '14-15', '16-17', '18-19', '20-21', '22-23'],\n            datasets: [{\n                label: '消息数量',\n                data: [3, 35, 42, 58, 76, 49, 67, 52, 39],\n                backgroundColor: '#FFA07A',\n                borderColor: '#FF6347',\n                borderWidth: 2,\n                tension: 0.3\n            }]\n        };\n\n        // 消息类型数据\n        const typeData = {\n            labels: ['技术讨论', '产品体验', '行业趋势', '闲聊', '其他'],\n            datasets: [{\n                data: [45, 30, 15, 8, 2],\n                backgroundColor: [\n                    '#FF7F50', '#FFA07A', '#FF6347', '#FF8C00', '#FF4500'\n                ]\n            }]\n        };\n\n        // 主题分布数据\n        const topicData = {\n            labels: ['智能体技术', 'AI产品', '合规问题', '创业讨论', '游戏应用'],\n            datasets: [{\n                label: '讨论热度',\n                data: [38, 32, 18, 8, 4],\n                backgroundColor: '#FF7F50',\n                borderColor: '#FF6347',\n                borderWidth: 1\n            }]\n        };\n\n        // 词云数据\n        const wordData = [\n            {text: \"智能体\", weight: 28},\n            {text: \"Agent\", weight: 25},\n            {text: \"模型\", weight: 22},\n            {text: \"提示词\", weight: 20},\n            {text: \"技术\", weight: 18},\n            {text: \"搜索\", weight: 16},\n            {text: \"研究\", weight: 15},\n            {text: \"合规\", weight: 14},\n            {text: \"应用\", weight: 13},\n            {text: \"游戏\", weight: 12},\n            {text: \"视频\", weight: 11},\n            {text: \"用户\", weight: 10},\n            {text: \"数据\", weight: 9},\n            {text: \"产品\", weight: 8},\n            {text: \"开发\", weight: 7},\n            {text: \"测试\", weight: 6},\n            {text: \"体验\", weight: 5},\n            {text: \"创业\", weight: 4},\n            {text: \"市场\", weight: 3}\n        ];\n\n        // 初始化图表\n        window.onload = function() {\n            // 用户消息量图表\n            new Chart(document.getElementById('userChart'), {\n                type: 'bar',\n                data: userData,\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {display: false},\n                        tooltip: {mode: 'index', intersect: false}\n                    },\n                    scales: {\n                        y: {beginAtZero: true, ticks: {color: '#5A3921'}}\n                    }\n                }\n            });\n\n            // 时间分布图表\n            new Chart(document.getElementById('timeChart'), {\n                type: 'line',\n                data: timeData,\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {display: false}\n                    },\n                    scales: {\n                        y: {beginAtZero: true, ticks: {color: '#5A3921'}}\n                    }\n                }\n            });\n\n            // 消息类型图表\n            new Chart(document.getElementById('typeChart'), {\n                type: 'doughnut',\n                data: typeData,\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {position: 'right', labels: {color: '#5A3921'}}\n                    }\n                }\n            });\n\n            // 主题分布图表\n            new Chart(document.getElementById('topicChart'), {\n                type: 'radar',\n                data: {\n                    labels: topicData.labels,\n                    datasets: [{\n                        label: '讨论热度',\n                        data: topicData.datasets[0].data,\n                        fill: true,\n                        backgroundColor: 'rgba(255, 160, 122, 0.2)',\n                        borderColor: '#FF6347',\n                        pointBackgroundColor: '#FF7F50'\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    scales: {\n                        r: {\n                            angleLines: {color: 'rgba(90, 57, 33, 0.1)'},\n                            grid: {color: 'rgba(90, 57, 33, 0.1)'},\n                            pointLabels: {color: '#5A3921'},\n                            ticks: {display: false, stepSize: 10}\n                        }\n                    }\n                }\n            });\n\n            // 生成词云\n            WordCloud(document.getElementById('wordcloud'), {\n                list: wordData,\n                gridSize: 15,\n                weightFactor: 5,\n                fontFamily: 'Segoe UI, Tahoma, sans-serif',\n                color: () => {\n                    const colors = ['#FF4500', '#FF6347', '#FF7F50', '#FF8C00', '#FFA07A'];\n                    return colors[Math.floor(Math.random() * colors.length)];\n                },\n                backgroundColor: '#FFF',\n                rotateRatio: 0.2,\n                rotationSteps: 3,\n                drawOutOfBound: false\n            });\n        };\n    </script>\n</body>\n</html>\n```", "savedAt": "2025-06-22T03:30:52.263Z"}