{"title": "[定时] 自定义分析 - AI产品蝗虫团", "groupName": "【1】AI产品蝗虫团", "analysisType": "custom", "timeRange": "2025-06-19~2025-06-19", "messageCount": 500, "isScheduled": true, "content": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>AI产品蝗虫团 - 聊天数据分析报告</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js\"></script>\n    <style>\n        :root {\n            --primary: #ff9a76;\n            --secondary: #ffb996;\n            --accent: #ff6b6b;\n            --light: #fff3e6;\n            --dark: #5c3d2e;\n            --text: #4a3f35;\n        }\n        \n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;\n        }\n        \n        body {\n            background: linear-gradient(135deg, var(--light) 0%, #ffe8d6 100%);\n            color: var(--text);\n            line-height: 1.6;\n            padding: 20px;\n            min-height: 100vh;\n        }\n        \n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n        }\n        \n        header {\n            text-align: center;\n            padding: 30px 0;\n            margin-bottom: 30px;\n            background: rgba(255, 255, 255, 0.7);\n            border-radius: 16px;\n            box-shadow: 0 8px 32px rgba(255, 154, 118, 0.2);\n            backdrop-filter: blur(4px);\n            border: 1px solid rgba(255, 255, 255, 0.3);\n        }\n        \n        h1 {\n            color: var(--accent);\n            font-size: 2.8rem;\n            margin-bottom: 10px;\n            text-shadow: 2px 2px 4px rgba(92, 61, 46, 0.1);\n        }\n        \n        .subtitle {\n            color: var(--dark);\n            font-size: 1.2rem;\n            max-width: 800px;\n            margin: 0 auto;\n        }\n        \n        .summary-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n            gap: 25px;\n            margin-bottom: 40px;\n        }\n        \n        .summary-card {\n            background: white;\n            border-radius: 16px;\n            padding: 25px;\n            box-shadow: 0 10px 30px rgba(255, 154, 118, 0.15);\n            transition: transform 0.3s ease, box-shadow 0.3s ease;\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            text-align: center;\n            border-top: 5px solid var(--primary);\n        }\n        \n        .summary-card:hover {\n            transform: translateY(-8px);\n            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.25);\n        }\n        \n        .summary-card h3 {\n            color: var(--accent);\n            margin-bottom: 15px;\n            font-size: 1.4rem;\n        }\n        \n        .summary-value {\n            font-size: 2.5rem;\n            font-weight: 700;\n            color: var(--dark);\n            margin: 10px 0;\n        }\n        \n        .section {\n            background: white;\n            border-radius: 16px;\n            padding: 35px;\n            margin-bottom: 40px;\n            box-shadow: 0 10px 30px rgba(255, 154, 118, 0.15);\n        }\n        \n        .section-title {\n            color: var(--accent);\n            font-size: 1.8rem;\n            margin-bottom: 25px;\n            padding-bottom: 15px;\n            border-bottom: 2px solid var(--secondary);\n            display: flex;\n            align-items: center;\n        }\n        \n        .section-title i {\n            margin-right: 12px;\n        }\n        \n        .top-users {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 20px;\n            margin-top: 20px;\n        }\n        \n        .user-card {\n            background: linear-gradient(135deg, var(--light) 0%, #ffd8be 100%);\n            border-radius: 12px;\n            padding: 20px;\n            flex: 1;\n            min-width: 200px;\n            text-align: center;\n            box-shadow: 0 5px 15px rgba(255, 154, 118, 0.1);\n        }\n        \n        .user-name {\n            font-weight: 700;\n            color: var(--accent);\n            margin-bottom: 10px;\n            font-size: 1.2rem;\n        }\n        \n        .user-messages {\n            font-size: 1.8rem;\n            font-weight: 700;\n            color: var(--dark);\n        }\n        \n        .chart-container {\n            height: 400px;\n            margin: 30px 0;\n            position: relative;\n        }\n        \n        .topic-container {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n            gap: 25px;\n            margin-top: 25px;\n        }\n        \n        .topic-card {\n            background: #fffaf5;\n            border-radius: 12px;\n            padding: 25px;\n            border-left: 4px solid var(--primary);\n            box-shadow: 0 5px 20px rgba(255, 154, 118, 0.1);\n        }\n        \n        .topic-card h4 {\n            color: var(--accent);\n            margin-bottom: 15px;\n            font-size: 1.3rem;\n        }\n        \n        .message-bubble {\n            background: var(--light);\n            border-radius: 18px;\n            padding: 15px;\n            margin: 15px 0;\n            position: relative;\n        }\n        \n        .message-bubble::after {\n            content: '';\n            position: absolute;\n            bottom: -10px;\n            left: 20px;\n            border-width: 10px 10px 0;\n            border-style: solid;\n            border-color: var(--light) transparent transparent;\n        }\n        \n        .message-header {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 8px;\n            font-size: 0.9rem;\n            color: var(--dark);\n        }\n        \n        .keyword-tag {\n            display: inline-block;\n            background: linear-gradient(to right, var(--primary), var(--accent));\n            color: white;\n            padding: 8px 18px;\n            border-radius: 50px;\n            margin: 8px;\n            font-weight: 600;\n            box-shadow: 0 4px 10px rgba(255, 107, 107, 0.3);\n        }\n        \n        .mermaid-container {\n            background: #fffaf5;\n            border-radius: 16px;\n            padding: 25px;\n            margin: 30px 0;\n            overflow: auto;\n            min-height: 300px;\n        }\n        \n        .quote-card {\n            background: linear-gradient(135deg, #ffd8be 0%, #ffb996 100%);\n            border-radius: 16px;\n            padding: 25px;\n            margin: 20px 0;\n            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.2);\n            position: relative;\n        }\n        \n        .quote-card::before {\n            content: \"\"\";\n            position: absolute;\n            top: 10px;\n            left: 15px;\n            font-size: 5rem;\n            color: rgba(255, 255, 255, 0.3);\n            font-family: Georgia, serif;\n        }\n        \n        .quote-text {\n            font-size: 1.3rem;\n            font-style: italic;\n            color: var(--dark);\n            margin-bottom: 15px;\n            position: relative;\n            z-index: 2;\n        }\n        \n        .quote-highlight {\n            color: var(--accent);\n            font-weight: 700;\n        }\n        \n        .quote-author {\n            text-align: right;\n            font-weight: 600;\n            color: var(--dark);\n        }\n        \n        .interpretation-area {\n            background: rgba(255, 255, 255, 0.7);\n            border-radius: 12px;\n            padding: 15px;\n            margin-top: 15px;\n            border-left: 3px solid var(--accent);\n        }\n        \n        .resources-list {\n            list-style: none;\n            padding: 20px 0;\n        }\n        \n        .resources-list li {\n            margin-bottom: 15px;\n            padding: 15px;\n            background: #fffaf5;\n            border-radius: 12px;\n            display: flex;\n            align-items: center;\n        }\n        \n        .resources-list li::before {\n            content: \"•\";\n            color: var(--accent);\n            font-weight: bold;\n            font-size: 1.5rem;\n            margin-right: 15px;\n        }\n        \n        footer {\n            text-align: center;\n            padding: 30px 0;\n            color: var(--dark);\n            font-size: 0.9rem;\n        }\n        \n        @media (max-width: 768px) {\n            .section {\n                padding: 25px 15px;\n            }\n            \n            .summary-grid {\n                grid-template-columns: 1fr;\n            }\n            \n            h1 {\n                font-size: 2rem;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header>\n            <h1>【1】AI产品蝗虫团 聊天数据分析</h1>\n            <p class=\"subtitle\">2025年6月19日 | 消息总数: 500 (有效文本: 420) | 活跃用户: 52人</p>\n        </header>\n        \n        <div class=\"summary-grid\">\n            <div class=\"summary-card\">\n                <h3>时间范围</h3>\n                <div class=\"summary-value\">00:02 - 14:56</div>\n                <p>全天持续活跃14小时</p>\n            </div>\n            \n            <div class=\"summary-card\">\n                <h3>核心发言用户</h3>\n                <div class=\"summary-value\">5人</div>\n                <p>贡献39%的消息量</p>\n            </div>\n            \n            <div class=\"summary-card\">\n                <h3>讨论高峰时段</h3>\n                <div class=\"summary-value\">09:00-11:00</div>\n                <p>集中45%的对话量</p>\n            </div>\n            \n            <div class=\"summary-card\">\n                <h3>热门产品提及</h3>\n                <div class=\"summary-value\">12+</div>\n                <p>AI工具/模型讨论</p>\n            </div>\n        </div>\n        \n        <section class=\"section\">\n            <h2 class=\"section-title\"><i class=\"fas fa-chart-bar\"></i> 核心数据可视化</h2>\n            \n            <div class=\"chart-container\">\n                <canvas id=\"activityChart\"></canvas>\n            </div>\n            \n            <div class=\"top-users\">\n                <div class=\"user-card\">\n                    <div class=\"user-name\">神的孩子在跳舞</div>\n                    <div class=\"user-messages\">84条</div>\n                    <p>核心贡献者</p>\n                </div>\n                <div class=\"user-card\">\n                    <div class=\"user-name\">Ro斯</div>\n                    <div class=\"user-messages\">41条</div>\n                    <p>工具专家</p>\n                </div>\n                <div class=\"user-card\">\n                    <div class=\"user-name\">年轮</div>\n                    <div class=\"user-messages\">24条</div>\n                    <p>技术探索者</p>\n                </div>\n                <div class=\"user-card\">\n                    <div class=\"user-name\">无我</div>\n                    <div class=\"user-messages\">23条</div>\n                    <p>产品体验官</p>\n                </div>\n                <div class=\"user-card\">\n                    <div class=\"user-name\">风林火山</div>\n                    <div class=\"user-messages\">22条</div>\n                    <p>行业观察员</p>\n                </div>\n            </div>\n        </section>\n        \n        <section class=\"section\">\n            <h2 class=\"section-title\"><i class=\"fas fa-key\"></i> 核心关键词分析</h2>\n            \n            <div style=\"text-align: center; margin: 30px 0;\">\n                <span class=\"keyword-tag\">AI播客生成</span>\n                <span class=\"keyword-tag\">多模态模型</span>\n                <span class=\"keyword-tag\">积分策略</span>\n                <span class=\"keyword-tag\">提示词优化</span>\n                <span class=\"keyword-tag\">RAG技术</span>\n                <span class=\"keyword-tag\">Agent系统</span>\n                <span class=\"keyword-tag\">模型对比</span>\n                <span class=\"keyword-tag\">中文TTS</span>\n            </div>\n            \n            <div class=\"mermaid-container\">\n                <div class=\"mermaid\">\nflowchart LR\n    A[AI播客生成] --> B(豆包TTS)\n    A --> C(NotebookLM)\n    B --> D[中文自然度突破]\n    C --> E[中文发音问题]\n    F[多模态模型] --> G(Google Gemini 2.5)\n    F --> H(Midjourney)\n    G --> I[音频双向交互]\n    H --> J[艺术独特性]\n    K[积分策略] --> L(Minimax)\n    K --> M(Manus AI)\n    L --> N[成本控制]\n    M --> O[免费策略]\n                </div>\n            </div>\n        </section>\n        \n        <section class=\"section\">\n            <h2 class=\"section-title\"><i class=\"fas fa-comments\"></i> 精华话题聚焦</h2>\n            \n            <div class=\"topic-container\">\n                <div class=\"topic-card\">\n                    <h4>AI播客生成技术突破</h4>\n                    <p>豆包AI在中文TTS领域取得重大突破，生成播客的自然度接近真人，尤其男声表现优异。</p>\n                    \n                    <div class=\"message-bubble\">\n                        <div class=\"message-header\">\n                            <span>神的孩子在跳舞</span>\n                            <span>07:32</span>\n                        </div>\n                        <div class=\"dialogue-content\">豆包这个中文真立马听不出来ai 尤其男声</div>\n                    </div>\n                    \n                    <div class=\"message-bubble\">\n                        <div class=\"message-header\">\n                            <span>风林火山</span>\n                            <span>11:31</span>\n                        </div>\n                        <div class=\"dialogue-content\">豆包AI播客，真人声音感暴强啊</div>\n                    </div>\n                </div>\n                \n                <div class=\"topic-card\">\n                    <h4>AI工具积分策略对比</h4>\n                    <p>群友深入讨论各AI平台的积分机制，分享薅羊毛技巧和成本控制方案。</p>\n                    \n                    <div class=\"message-bubble\">\n                        <div class=\"message-header\">\n                            <span>Ro斯</span>\n                            <span>09:19</span>\n                        </div>\n                        <div class=\"dialogue-content\">某鱼五块钱买一个2800积分号，一天买一个够用，充值亏死了</div>\n                    </div>\n                    \n                    <div class=\"message-bubble\">\n                        <div class=\"message-header\">\n                            <span></span>\n                            <span>09:44</span>\n                        </div>\n                        <div class=\"dialogue-content\">直接上OpenRouter 选gemini-2.5-flash-lite 这个模型便宜的很</div>\n                    </div>\n                </div>\n            </div>\n        </section>\n        \n        <section class=\"section\">\n            <h2 class=\"section-title\"><i class=\"fas fa-star\"></i> 群友金句洞察</h2>\n            \n            <div class=\"quote-card\">\n                <div class=\"quote-text\">\"<span class=\"quote-highlight\">豆包这个中文真立马听不出来ai</span> 尤其男声\"</div>\n                <div class=\"quote-author\">神的孩子在跳舞 | 07:32</div>\n                <div class=\"interpretation-area\">\n                    <p><strong>AI解读：</strong>中文TTS技术取得突破性进展，标志着AI生成内容在语音自然度上接近人类水平，对内容创作领域将产生深远影响。</p>\n                </div>\n            </div>\n            \n            <div class=\"quote-card\">\n                <div class=\"quote-text\">\"<span class=\"quote-highlight\">现在小学的孩子还看过真人视频</span>，等3年后出生的孩子，估计从小就是全生成视频了\"</div>\n                <div class=\"quote-author\">刘博 | 12:07</div>\n                <div class=\"interpretation-area\">\n                    <p><strong>AI解读：</strong>指出生成式AI将重塑新一代的数字内容消费习惯和审美标准，未来教育需重视AI素养培养。</p>\n                </div>\n            </div>\n            \n            <div class=\"quote-card\">\n                <div class=\"quote-text\">\"<span class=\"quote-highlight\">客观的评估那就是有实验的一套评估方法</span>，看他们那些测试方法基本转换成为我们的自己的一套测试方法就是场景化\"</div>\n                <div class=\"quote-author\">神的孩子在跳舞 | 14:18</div>\n                <div class=\"interpretation-area\">\n                    <p><strong>AI解读：</strong>强调大模型评估需要从实验室指标转向实际应用场景，反映行业从技术导向到价值导向的转变。</p>\n                </div>\n            </div>\n        </section>\n        \n        <section class=\"section\">\n            <h2 class=\"section-title\"><i class=\"fas fa-cube\"></i> 热门产品与资源</h2>\n            \n            <ul class=\"resources-list\">\n                <li><strong>豆包AI</strong>：字节推出的AI助手，最新上线高质量中文TTS播客功能</li>\n                <li><strong>Gemini 2.5</strong>：谷歌多模态模型，实现音频双向交互和高质量TTS生成</li>\n                <li><strong>Manus AI</strong>：支持视频生成的AI平台，提供免费积分策略</li>\n                <li><strong>OpenRouter</strong>：模型聚合平台，提供gemini-2.5-flash-lite等经济型API</li>\n                <li><strong>Novita.ai</strong>：多模型平台，注册赠送$10额度，支持海螺Minmax等模型</li>\n            </ul>\n        </section>\n        \n        <footer>\n            <p>数据来源：2025-06-19群聊记录 | 分析时间：2025-06-20</p>\n            <p>© 2025 AI产品蝗虫团数据分析报告 | 可视化采用Chart.js</p>\n        </footer>\n    </div>\n\n    <script>\n        // 初始化Mermaid\n        mermaid.initialize({\n            startOnLoad: true,\n            theme: 'base',\n            themeVariables: {\n                primaryColor: '#ffd8be',\n                nodeBorder: '#ff9a76',\n                lineColor: '#ff6b6b',\n                textColor: '#5c3d2e'\n            }\n        });\n        \n        // 活动趋势图表\n        document.addEventListener('DOMContentLoaded', function() {\n            const ctx = document.getElementById('activityChart').getContext('2d');\n            new Chart(ctx, {\n                type: 'line',\n                data: {\n                    labels: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00'],\n                    datasets: [{\n                        label: '消息数量',\n                        data: [18, 8, 3, 12, 48, 76, 42, 35],\n                        backgroundColor: 'rgba(255, 154, 118, 0.2)',\n                        borderColor: '#ff6b6b',\n                        borderWidth: 3,\n                        pointBackgroundColor: '#ff9a76',\n                        pointRadius: 6,\n                        tension: 0.3,\n                        fill: true\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {\n                            labels: {\n                                font: {\n                                    size: 16\n                                }\n                            }\n                        }\n                    },\n                    scales: {\n                        y: {\n                            beginAtZero: true,\n                            grid: {\n                                color: 'rgba(255, 185, 150, 0.1)'\n                            },\n                            ticks: {\n                                font: {\n                                    size: 14\n                                }\n                            }\n                        },\n                        x: {\n                            grid: {\n                                color: 'rgba(255, 185, 150, 0.1)'\n                            },\n                            ticks: {\n                                font: {\n                                    size: 14\n                                }\n                            }\n                        }\n                    }\n                }\n            });\n        });\n    </script>\n</body>\n</html>", "savedAt": "2025-06-19T17:11:46.612Z"}