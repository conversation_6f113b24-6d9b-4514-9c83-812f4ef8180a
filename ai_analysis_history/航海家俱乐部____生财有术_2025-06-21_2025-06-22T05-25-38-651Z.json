{"groupName": "航海家俱乐部🗺️｜生财有术", "analysisType": "custom", "timeRange": "2025-06-21", "messageCount": 59, "timestamp": "2025-06-22T05:25:38.651Z", "title": "航海家俱乐部🗺️｜生财有术 - 自定义分析", "content": "```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>航海家俱乐部🗺️｜生财有术 - 2025年06月21日 聊天精华报告</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js\"></script>\n    <style>\n        :root {\n            --bg-main: #FFFBF5;\n            --bg-card: rgba(255, 255, 255, 0.85);\n            --bg-card-alt: #FFF5E1;\n            --text-primary: #4A2E27;\n            --text-secondary: #7A5C55;\n            --accent-primary: #E58A43;\n            --accent-secondary: #F7C5A0;\n            --border-color: #F0E5D8;\n            --shadow-color: rgba(140, 91, 47, 0.1);\n        }\n\n        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');\n\n        * {\n            box-sizing: border-box;\n            margin: 0;\n            padding: 0;\n        }\n\n        html {\n            scroll-behavior: smooth;\n        }\n\n        body {\n            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\n            background-color: var(--bg-main);\n            color: var(--text-primary);\n            line-height: 1.8;\n            font-size: 16px;\n        }\n\n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n            padding: 2rem;\n        }\n\n        header {\n            text-align: center;\n            margin-bottom: 3rem;\n        }\n\n        header h1 {\n            font-size: 2.5rem;\n            font-weight: 700;\n            color: var(--text-primary);\n            margin-bottom: 0.5rem;\n        }\n\n        header p {\n            font-size: 1.1rem;\n            color: var(--text-secondary);\n        }\n\n        .bento-grid {\n            display: grid;\n            gap: 1.5rem;\n            grid-template-columns: repeat(4, 1fr);\n            grid-auto-rows: minmax(100px, auto);\n        }\n\n        .card {\n            background-color: var(--bg-card);\n            border: 1px solid var(--border-color);\n            border-radius: 20px;\n            padding: 1.5rem 2rem;\n            box-shadow: 0 8px 25px var(--shadow-color);\n            backdrop-filter: blur(10px);\n            transition: transform 0.3s ease, box-shadow 0.3s ease;\n        }\n        \n        .card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 12px 35px rgba(140, 91, 47, 0.15);\n        }\n\n        .card-1x1 { grid-column: span 1; }\n        .card-2x1 { grid-column: span 2; }\n        .card-2x2 { grid-column: span 2; grid-row: span 2; }\n        .card-4x1 { grid-column: span 4; }\n\n        .card-title {\n            font-size: 1.5rem;\n            font-weight: 700;\n            margin-bottom: 1rem;\n            color: var(--text-primary);\n            display: flex;\n            align-items: center;\n        }\n        \n        .card-title i {\n            margin-right: 0.75rem;\n            color: var(--accent-primary);\n        }\n\n        .summary-list {\n            list-style: none;\n            padding: 0;\n        }\n\n        .summary-list li {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            padding: 0.75rem 0;\n            border-bottom: 1px solid var(--border-color);\n            font-size: 1rem;\n        }\n\n        .summary-list li:last-child {\n            border-bottom: none;\n        }\n\n        .summary-list li strong {\n            font-weight: 500;\n            color: var(--text-secondary);\n        }\n        \n        .summary-list li span {\n            font-weight: 700;\n            color: var(--accent-primary);\n        }\n\n        .keyword-cloud {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 0.75rem;\n            align-items: center;\n        }\n        \n        .keyword-tag {\n            background-color: var(--bg-card-alt);\n            color: var(--text-secondary);\n            padding: 0.5rem 1rem;\n            border-radius: 30px;\n            font-size: 0.9rem;\n            font-weight: 500;\n            border: 1px solid var(--accent-secondary);\n            transition: all 0.3s ease;\n        }\n        \n        .keyword-tag:hover {\n            background-color: var(--accent-secondary);\n            color: var(--text-primary);\n            transform: scale(1.05);\n        }\n        \n        .mermaid {\n            width: 100%;\n            height: 100%;\n            min-height: 400px;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n        }\n\n        .topic-card {\n            margin-bottom: 2rem;\n            padding: 2rem;\n            background: linear-gradient(135deg, #fffcf7, #fff7ea);\n            border-radius: 15px;\n            border: 1px solid var(--border-color);\n        }\n\n        .topic-card h3 {\n            font-size: 1.3rem;\n            font-weight: 700;\n            margin-bottom: 0.75rem;\n            color: var(--accent-primary);\n        }\n\n        .topic-card .description {\n            margin-bottom: 1.5rem;\n            color: var(--text-secondary);\n            font-size: 1rem;\n        }\n        \n        .dialogue-container {\n            background-color: #fff;\n            border: 1px solid var(--border-color);\n            border-radius: 10px;\n            padding: 1.5rem;\n        }\n\n        .dialogue-container h4 {\n            font-size: 1.1rem;\n            font-weight: 500;\n            margin-bottom: 1rem;\n            color: var(--text-secondary);\n            border-bottom: 1px dashed var(--border-color);\n            padding-bottom: 0.5rem;\n        }\n\n        .message {\n            margin-bottom: 1rem;\n        }\n\n        .message:last-child {\n            margin-bottom: 0;\n        }\n\n        .message-header {\n            display: flex;\n            align-items: center;\n            margin-bottom: 0.25rem;\n        }\n\n        .message-sender {\n            font-weight: 700;\n            color: var(--text-primary);\n            margin-right: 0.5rem;\n        }\n\n        .message-time {\n            font-size: 0.8rem;\n            color: var(--accent-secondary);\n        }\n        \n        .message-content {\n            background-color: var(--bg-main);\n            padding: 0.75rem 1rem;\n            border-radius: 10px;\n            border-left: 3px solid var(--accent-secondary);\n            color: var(--text-secondary);\n        }\n\n        .quote-card {\n            background: var(--bg-card-alt);\n            border-radius: 15px;\n            padding: 1.5rem;\n            display: flex;\n            flex-direction: column;\n            border-left: 4px solid var(--accent-primary);\n        }\n\n        .quote-text {\n            font-size: 1.1rem;\n            font-weight: 500;\n            margin-bottom: 1rem;\n            flex-grow: 1;\n            position: relative;\n            padding-left: 2rem;\n        }\n        \n        .quote-text::before {\n            content: '“';\n            font-family: 'Times New Roman', Times, serif;\n            position: absolute;\n            left: -0.25rem;\n            top: -1rem;\n            font-size: 4rem;\n            color: var(--accent-secondary);\n            opacity: 0.8;\n        }\n\n        .quote-author {\n            text-align: right;\n            font-weight: 700;\n            color: var(--text-primary);\n            margin-bottom: 1rem;\n        }\n        \n        .quote-interpretation {\n            font-size: 0.9rem;\n            color: var(--text-secondary);\n            background-color: rgba(255,255,255,0.5);\n            padding: 0.75rem;\n            border-radius: 8px;\n            border-top: 1px solid var(--border-color);\n        }\n        \n        .resource-list a {\n            display: block;\n            padding: 1rem;\n            margin-bottom: 1rem;\n            border-radius: 10px;\n            background-color: var(--bg-card-alt);\n            color: var(--accent-primary);\n            font-weight: 500;\n            text-decoration: none;\n            transition: all 0.3s ease;\n            border: 1px solid var(--accent-secondary);\n        }\n        \n        .resource-list a:hover {\n            background-color: var(--accent-secondary);\n            color: var(--text-primary);\n            transform: translateX(5px);\n        }\n        \n        .resource-list p {\n            padding: 1rem;\n            margin-bottom: 1rem;\n            border-radius: 10px;\n            background-color: var(--bg-card-alt);\n            border: 1px solid var(--accent-secondary);\n        }\n\n        .resource-list p strong {\n            color: var(--accent-primary);\n        }\n\n        footer {\n            text-align: center;\n            margin-top: 4rem;\n            padding: 2rem;\n            font-size: 0.9rem;\n            color: var(--text-secondary);\n        }\n\n        @media (max-width: 992px) {\n            .bento-grid {\n                grid-template-columns: repeat(2, 1fr);\n            }\n        }\n\n        @media (max-width: 768px) {\n            .bento-grid {\n                grid-template-columns: 1fr;\n            }\n            .card-1x1, .card-2x1, .card-2x2, .card-4x1 {\n                grid-column: span 1;\n                grid-row: span 1;\n            }\n            header h1 {\n                font-size: 2rem;\n            }\n            .card {\n                padding: 1.5rem;\n            }\n        }\n    </style>\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n</head>\n<body>\n\n    <div class=\"container\">\n        <header>\n            <h1>航海家俱乐部🗺️｜生财有术</h1>\n            <p>2025年06月21日 聊天精华报告</p>\n        </header>\n\n        <main class=\"bento-grid\">\n            <div class=\"card card-2x2\">\n                <h2 class=\"card-title\"><i class=\"fa-solid fa-chart-pie\"></i>数据总览</h2>\n                <ul class=\"summary-list\">\n                    <li><strong>日期</strong><span>2025-06-21</span></li>\n                    <li><strong>消息总数</strong><span>59</span></li>\n                    <li><strong>有效文本消息</strong><span>50</span></li>\n                    <li><strong>活跃用户数</strong><span>21</span></li>\n                    <li><strong>时间范围</strong><span>02:30 - 23:55</span></li>\n                </ul>\n                <canvas id=\"topSpeakersChart\"></canvas>\n            </div>\n\n            <div class=\"card card-2x1\">\n                <h2 class=\"card-title\"><i class=\"fa-solid fa-tags\"></i>核心关键词</h2>\n                <div class=\"keyword-cloud\">\n                    <span class=\"keyword-tag\">朋友圈折叠</span>\n                    <span class=\"keyword-tag\">重资产投资</span>\n                    <span class=\"keyword-tag\">水电站收购</span>\n                    <span class=\"keyword-tag\">深海圈现场</span>\n                    <span class=\"keyword-tag\">AI 使用技巧</span>\n                    <span class=\"keyword-tag\">黑客马拉松</span>\n                    <span class=\"keyword-tag\">小排老师</span>\n                    <span class=\"keyword-tag\">AI 编程变现</span>\n                    <span class=\"keyword-tag\">ChatGPT 提示词</span>\n                    <span class=\"keyword-tag\">生财有术</span>\n                </div>\n            </div>\n\n            <div class=\"card card-2x1\">\n                 <h2 class=\"card-title\"><i class=\"fa-solid fa-share-nodes\"></i>核心概念关系图</h2>\n                <div class=\"mermaid\">\ngraph LR\n    subgraph \"生财有术生态\"\n        A(\"航海家俱乐部\")\n    end\n\n    subgraph \"核心讨论议题\"\n        B(\"朋友圈折叠策略\")\n        C(\"重资产投资\")\n        D(\"线下活动 (深海圈)\")\n    end\n    \n    subgraph \"AI应用与实践\"\n        E(\"AI使用技巧\")\n        F(\"AI编程变现\")\n        G(\"ChatGPT提示词\")\n        H(\"小排老师分享\")\n    end\n    \n    A -- 讨论 --> B\n    A -- 讨论 --> C\n    A -- 讨论 --> D\n    \n    C --> I(\"水电站收购\")\n    D -- 产出 --> E\n    E -- 来源 --> H\n    E -- 包含 --> G\n    \n    A -- 关注 --> F\n\n    classDef default fill:#FFFBF5,stroke:#E58A43,stroke-width:2px,color:#4A2E27;\n    classDef subgraph fill:#FFF5E1,stroke:#F7C5A0,color:#7A5C55;\n    class A,B,C,D,E,F,G,H,I subgraph\n                </div>\n            </div>\n\n            <div class=\"card card-4x1\">\n                <h2 class=\"card-title\"><i class=\"fa-solid fa-comments\"></i>精华话题聚焦</h2>\n                \n                <div class=\"topic-card\">\n                    <h3><i class=\"fa-solid fa-lightbulb\"></i>话题一：AI 实战技巧与线下活动复盘</h3>\n                    <p class=\"description\">深夜的群聊因“深海圈”线下活动的复盘而再次活跃。秀儿、Okada 欢欢等人分享了现场的“卷”和热情。核心价值在于梁梦吟Catherine的无私分享，她详细记述了从小排老师那里学到的AI使用心得，特别是那个极具启发性的ChatGPT提示词：“认真一点，不然不给你续费了”。这个具体的、可立即应用的技巧，引发了群友的极大兴趣和讨论，展现了社群对于高价值、可实操信息的渴望。</p>\n                    <div class=\"dialogue-container\">\n                        <h4><i class=\"fa-regular fa-comment-dots\"></i>重要对话节选</h4>\n                        <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">秀儿</span><span class=\"message-time\">22:20:47</span></div>\n                            <p class=\"message-content\">今天的深海圈现场，太卷了</p>\n                        </div>\n                        <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">Okada 欢欢</span><span class=\"message-time\">22:24:45</span></div>\n                            <p class=\"message-content\">冲击黑客马拉松，干到24点</p>\n                        </div>\n                        <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">秀儿</span><span class=\"message-time\">23:36:42</span></div>\n                            <p class=\"message-content\">@梦吟～生财有术产品经理 今天收获咋样，有没有一些小诀窍可以和我们航海家分享的[坏笑]</p>\n                        </div>\n                        <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">梁梦吟Catherine</span><span class=\"message-time\">23:38:15</span></div>\n                            <p class=\"message-content\">学习到了小排老师是怎么使用AI的，贴身围观他日常和AI的互动过程。收获还是蛮大的[憨笑]</p>\n                        </div>\n                        <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">梁梦吟Catherine</span><span class=\"message-time\">23:47:16</span></div>\n                            <p class=\"message-content\">简单记了一些……课程全程以实操为主，认知类的，差不多就记了这些了。信息密度太高了，节奏挺快的。如果对某个点，有想了解细节的，可以再找我交流～</p>\n                        </div>\n                        <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">Albert</span><span class=\"message-time\">23:47:45</span></div>\n                            <p class=\"message-content\">认真一点，不然不给你续费了</p>\n                        </div>\n                        <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">Albert</span><span class=\"message-time\">23:47:48</span></div>\n                            <p class=\"message-content\">今日最佳</p>\n                        </div>\n                        <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">秀儿</span><span class=\"message-time\">23:50:46</span></div>\n                            <p class=\"message-content\">Chatgpt提示词：认识一点，不然不给你续费了<br><br>马上用起来！</p>\n                        </div>\n                    </div>\n                </div>\n\n                <div class=\"topic-card\">\n                    <h3><i class=\"fa-solid fa-water\"></i>话题二：重资产项目投资探讨</h3>\n                    <p class=\"description\">由路远先生发起，关于收购西南片区水电站和金属矿的重资产业务讨论，迅速吸引了群内成员的注意。秀儿以“哇，这么重资产”表达了普遍的惊讶，并追问其商业模式。讨论虽然不长，但揭示了群内成员业务范围的广度和深度，从轻量级的AI应用到重量级的实体投资均有涉猎。路远先生最后点出有基金参与，为这个看似遥远的业务提供了现实的立足点。</p>\n                    <div class=\"dialogue-container\">\n                        <h4><i class=\"fa-regular fa-comment-dots\"></i>重要对话节选</h4>\n                        <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">路远先生</span><span class=\"message-time\">11:58:54</span></div>\n                            <p class=\"message-content\">我们最近新增加了一个业务，收购西南片区正在运营的水电站和各种金属矿，各位航海家有资源的可以联系我[握手]</p>\n                        </div>\n                        <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">秀儿</span><span class=\"message-time\">12:01:32</span></div>\n                            <p class=\"message-content\">哇，这么重资产。</p>\n                        </div>\n                         <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">秀儿</span><span class=\"message-time\">12:01:50</span></div>\n                            <p class=\"message-content\">这个是怎么赚钱运作的呀[天啊]</p>\n                        </div>\n                         <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">越越｜致力于工作流研究</span><span class=\"message-time\">12:02:01</span></div>\n                            <p class=\"message-content\">贷款？</p>\n                        </div>\n                         <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">刺猬🐬</span><span class=\"message-time\">12:19:04</span></div>\n                            <p class=\"message-content\">挖矿哈哈哈</p>\n                        </div>\n                        <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">路远先生</span><span class=\"message-time\">16:34:56</span></div>\n                            <p class=\"message-content\">有基金在投资</p>\n                        </div>\n                    </div>\n                </div>\n\n                <div class=\"topic-card\">\n                    <h3><i class=\"fa-solid fa-eye-slash\"></i>话题三：微信朋友圈内容折叠规则讨论</h3>\n                    <p class=\"description\">有尔🌙关于朋友圈发布间隔4小时仍被折叠的疑问，引发了一场关于平台规则的小范围探讨。越越｜致力于工作流研究和邹老师等成员参与讨论，得出的共识是：当前微信对具有营销性质的内容审核趋严，无论发布间隔多久，只要被系统判定为营销，就有可能被折叠。这个讨论反映了社群成员对私域流量运营环境变化的敏感和及时应对。</p>\n                    <div class=\"dialogue-container\">\n                        <h4><i class=\"fa-regular fa-comment-dots\"></i>重要对话节选</h4>\n                        <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">有尔🌙</span><span class=\"message-time\">16:28:58</span></div>\n                            <p class=\"message-content\">🙋‍♀️举手# 刚发现间隔4小时发的朋友圈都折叠了，[捂脸]请问现在最新规则是间隔多少时间不折叠？ 知道留给私域的时间不多了，没想到这么快</p>\n                        </div>\n                        <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">越越｜致力于工作流研究</span><span class=\"message-time\">16:31:21</span></div>\n                            <p class=\"message-content\">有营销性质的都会折叠</p>\n                        </div>\n                         <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">有尔🌙</span><span class=\"message-time\">16:32:00</span></div>\n                            <p class=\"message-content\">不管间隔多久都折叠？</p>\n                        </div>\n                         <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">越越｜致力于工作流研究</span><span class=\"message-time\">16:34:03</span></div>\n                            <p class=\"message-content\">是啊，昨天发的都一起折叠了</p>\n                        </div>\n                         <div class=\"message\">\n                            <div class=\"message-header\"><span class=\"message-sender\">邹老师</span><span class=\"message-time\">16:34:31</span></div>\n                            <p class=\"message-content\">@有尔🌙 手动编辑发的也折叠了？</p>\n                        </div>\n                    </div>\n                </div>\n\n            </div>\n\n            <div class=\"card card-4x1\">\n                <h2 class=\"card-title\"><i class=\"fa-solid fa-star\"></i>群友金句闪耀</h2>\n                <div class=\"bento-grid\">\n                     <div class=\"card-2x1 quote-card\">\n                        <p class=\"quote-text\">认真一点，不然不给你续费了</p>\n                        <p class=\"quote-author\">- Albert</p>\n                        <div class=\"quote-interpretation\">\n                            <strong>AI解读：</strong> 这句话从一句玩笑式的“批评”升华为一个高效的ChatGPT指令，是社群智慧的绝佳体现。它揭示了与AI协作的核心：用直接、带有后果暗示的语言，可以激发AI模型产出更高质量、更“严肃”的内容。这不仅是一个技巧，更是一种人机交互的哲学。\n                        </div>\n                    </div>\n                     <div class=\"card-2x1 quote-card\">\n                        <p class=\"quote-text\">AI 编程确实趋势，文案产品上线一个月就变现近 10W，都没搞投放。</p>\n                        <p class=\"quote-author\">- #周知2.0</p>\n                        <div class=\"quote-interpretation\">\n                            <strong>AI解读：</strong> 这是一条信息量巨大的实战捷报。它用具体的数字（10W变现）和事实（无投放）强有力地证明了AI产品化的巨大潜力和低门槛启动的可能性。这句话对群内成员是极大的鼓舞，是理论趋势的最佳现实注脚。\n                        </div>\n                    </div>\n                     <div class=\"card-2x1 quote-card\">\n                        <p class=\"quote-text\">哇，这么重资产。</p>\n                        <p class=\"quote-author\">- 秀儿</p>\n                        <div class=\"quote-interpretation\">\n                            <strong>AI解读：</strong> 简短的四个字生动地捕捉了群友在听到“收购水电站”时的第一反应。它代表了从数字世界、轻资产创业模式，到传统重资产领域的巨大跨度，也体现了社群成员对多元化商业模式的开放心态和好奇心。\n                        </div>\n                    </div>\n                     <div class=\"card-2x1 quote-card\">\n                        <p class=\"quote-text\">学习到了小排老师是怎么使用AI的，贴身围观他日常和AI的互动过程。</p>\n                        <p class=\"quote-author\">- 梁梦吟Catherine</p>\n                        <div class=\"quote-interpretation\">\n                            <strong>AI解读：</strong> 这句话强调了“干中学”和“跟高手学”的价值。它点明了学习AI最有效的方式之一，并非仅仅是看教程，而是观察专家的实际操作流程（workflow）。这反映了社群对深度、沉浸式学习方法的推崇。\n                        </div>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"card card-4x1\">\n                <h2 class=\"card-title\"><i class=\"fa-solid fa-paperclip\"></i>提及产品与资源</h2>\n                <div class=\"resource-list\">\n                    <p><strong>[产品概念] AI文案产品:</strong> 由#周知2.0提及，是一种利用AI技术自动生成文案内容的应用，展现了强大的商业变现能力。</p>\n                    <a href=\"https://s296cam1nz.feishu.cn/docx/ObyfdpyTloOGtVxAG6HcgB1en4g?from=from_copylink\" target=\"_blank\"><i class=\"fa-solid fa-link\"></i> 飞书文档：昨晚直播课件</a>\n                </div>\n            </div>\n\n        </main>\n\n        <footer>\n            <p>由专业数据分析师和前端开发工程师AI生成</p>\n            <p>报告生成时间: 2025-06-22</p>\n        </footer>\n    </div>\n    \n    <script type=\"module\">\n        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';\n        \n        mermaid.initialize({\n            startOnLoad: true,\n            theme: 'base',\n            themeVariables: {\n                primaryColor: '#FFFBF5',\n                primaryTextColor: '#4A2E27',\n                primaryBorderColor: '#E58A43',\n                lineColor: '#F7C5A0',\n                secondaryColor: '#FFF5E1',\n                tertiaryColor: '#fff',\n                nodeTextColor: '#4A2E27',\n                mainBkg: '#FFF5E1',\n                clusterBkg: '#FFFBF5',\n                clusterBorder: '#F7C5A0',\n                defaultLinkColor: '#7A5C55',\n                fontFamily: \"'Noto Sans SC', sans-serif\",\n            }\n        });\n    </script>\n\n    <script>\n        document.addEventListener('DOMContentLoaded', function () {\n            const ctx = document.getElementById('topSpeakersChart').getContext('2d');\n            \n            const topSpeakersData = {\n                labels: ['秀儿', '坤大汀', '梁梦吟Catherine', '芷蓝', '越越｜致力于...'],\n                datasets: [{\n                    label: '消息数量',\n                    data: [11, 5, 4, 3, 3],\n                    backgroundColor: [\n                        '#E58A43',\n                        '#F7C5A0',\n                        '#FFDAB9',\n                        '#FFE4B5',\n                        '#FFF5E1'\n                    ],\n                    borderColor: [\n                        '#4A2E27'\n                    ],\n                    borderWidth: 1,\n                    borderRadius: 5,\n                }]\n            };\n\n            const topSpeakersConfig = {\n                type: 'bar',\n                data: topSpeakersData,\n                options: {\n                    indexAxis: 'y',\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {\n                            display: false\n                        },\n                        title: {\n                            display: true,\n                            text: '发言最活跃用户 TOP 5',\n                            color: '#4A2E27',\n                            font: {\n                                size: 16,\n                                family: \"'Noto Sans SC', sans-serif\",\n                                weight: '500'\n                            },\n                            padding: {\n                                bottom: 20\n                            }\n                        }\n                    },\n                    scales: {\n                        y: {\n                            ticks: {\n                                color: '#7A5C55',\n                                font: {\n                                  family: \"'Noto Sans SC', sans-serif\",\n                                }\n                            },\n                             grid: {\n                                display: false\n                            }\n                        },\n                        x: {\n                            ticks: {\n                                color: '#7A5C55',\n                                font: {\n                                  family: \"'Noto Sans SC', sans-serif\",\n                                }\n                            },\n                            grid: {\n                                color: '#F0E5D8'\n                            }\n                        }\n                    }\n                }\n            };\n\n            new Chart(ctx, topSpeakersConfig);\n        });\n    </script>\n</body>\n</html>\n```", "savedAt": "2025-06-22T05:25:38.651Z"}