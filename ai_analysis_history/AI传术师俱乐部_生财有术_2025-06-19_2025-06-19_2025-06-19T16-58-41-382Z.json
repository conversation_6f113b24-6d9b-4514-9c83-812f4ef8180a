{"title": "[定时] 自定义分析 - AI传术师俱乐部", "groupName": "AI传术师俱乐部|生财有术", "analysisType": "custom", "timeRange": "2025-06-19~2025-06-19", "messageCount": 326, "isScheduled": true, "content": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>AI传术师俱乐部|生财有术 - 聊天数据分析</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js\"></script>\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n    <style>\n        :root {\n            --primary: #FFA726;\n            --secondary: #FFD54F;\n            --accent: #FF7043;\n            --background: #FFF8E1;\n            --card-bg: #FFFFFF;\n            --text: #5D4037;\n            --light-text: #8D6E63;\n            --border: #FFCC80;\n            --shadow: rgba(255, 167, 38, 0.2);\n        }\n        \n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n        \n        body {\n            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;\n            background-color: var(--background);\n            color: var(--text);\n            line-height: 1.6;\n            padding: 20px;\n        }\n        \n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n        }\n        \n        header {\n            text-align: center;\n            padding: 30px 0;\n            margin-bottom: 30px;\n            background: linear-gradient(135deg, var(--primary), var(--secondary));\n            border-radius: 16px;\n            color: white;\n            box-shadow: 0 8px 20px var(--shadow);\n        }\n        \n        h1 {\n            font-size: 2.8rem;\n            margin-bottom: 10px;\n            text-shadow: 0 2px 4px rgba(0,0,0,0.1);\n        }\n        \n        .subtitle {\n            font-size: 1.2rem;\n            opacity: 0.9;\n            max-width: 800px;\n            margin: 0 auto;\n        }\n        \n        .summary-cards {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n            gap: 20px;\n            margin-bottom: 40px;\n        }\n        \n        .card {\n            background: var(--card-bg);\n            border-radius: 16px;\n            padding: 25px;\n            box-shadow: 0 5px 15px var(--shadow);\n            transition: transform 0.3s ease, box-shadow 0.3s ease;\n        }\n        \n        .card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 8px 25px rgba(255, 112, 67, 0.3);\n        }\n        \n        .card-title {\n            display: flex;\n            align-items: center;\n            gap: 10px;\n            color: var(--accent);\n            margin-bottom: 15px;\n            font-size: 1.3rem;\n        }\n        \n        .card-content {\n            font-size: 1.8rem;\n            font-weight: 700;\n            color: var(--primary);\n        }\n        \n        .card-content small {\n            font-size: 1rem;\n            color: var(--light-text);\n            display: block;\n            margin-top: 5px;\n        }\n        \n        .section-title {\n            display: flex;\n            align-items: center;\n            gap: 10px;\n            margin: 40px 0 20px;\n            color: var(--accent);\n            font-size: 1.8rem;\n            border-bottom: 2px solid var(--border);\n            padding-bottom: 10px;\n        }\n        \n        .chart-container {\n            background: var(--card-bg);\n            border-radius: 16px;\n            padding: 25px;\n            margin-bottom: 40px;\n            box-shadow: 0 5px 15px var(--shadow);\n        }\n        \n        .grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n            gap: 25px;\n            margin-bottom: 40px;\n        }\n        \n        .topic-card {\n            background: var(--card-bg);\n            border-radius: 16px;\n            padding: 25px;\n            box-shadow: 0 5px 15px var(--shadow);\n        }\n        \n        .topic-header {\n            display: flex;\n            align-items: center;\n            gap: 10px;\n            margin-bottom: 15px;\n            color: var(--accent);\n        }\n        \n        .topic-content {\n            background: #FFF3E0;\n            border-radius: 12px;\n            padding: 15px;\n            margin-bottom: 20px;\n        }\n        \n        .message {\n            margin-bottom: 15px;\n            padding: 15px;\n            border-radius: 12px;\n            background: #FFF8E1;\n            border-left: 4px solid var(--primary);\n        }\n        \n        .message-header {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 8px;\n            color: var(--light-text);\n            font-size: 0.9rem;\n        }\n        \n        .keyword-tag {\n            display: inline-block;\n            background: #FFE0B2;\n            color: #E65100;\n            padding: 5px 15px;\n            border-radius: 20px;\n            margin: 5px;\n            font-weight: 600;\n            box-shadow: 0 2px 5px rgba(0,0,0,0.05);\n        }\n        \n        .quote-card {\n            background: linear-gradient(135deg, #FFF3E0, #FFECB3);\n            border-radius: 16px;\n            padding: 25px;\n            box-shadow: 0 5px 15px var(--shadow);\n            position: relative;\n            overflow: hidden;\n        }\n        \n        .quote-card::before {\n            content: \"\"\";\n            position: absolute;\n            top: 10px;\n            left: 15px;\n            font-size: 5rem;\n            color: rgba(255, 167, 38, 0.2);\n            font-family: serif;\n            line-height: 1;\n        }\n        \n        .quote-text {\n            font-size: 1.2rem;\n            font-style: italic;\n            margin-bottom: 15px;\n            position: relative;\n            z-index: 2;\n        }\n        \n        .quote-highlight {\n            color: var(--accent);\n            font-weight: 700;\n        }\n        \n        .quote-author {\n            text-align: right;\n            color: var(--light-text);\n            font-size: 0.9rem;\n            font-style: italic;\n        }\n        \n        .mermaid-container {\n            background: #FFF8E1;\n            border-radius: 16px;\n            padding: 25px;\n            margin: 40px 0;\n            box-shadow: 0 5px 15px var(--shadow);\n            overflow: auto;\n        }\n        \n        @media (max-width: 768px) {\n            .grid {\n                grid-template-columns: 1fr;\n            }\n            \n            h1 {\n                font-size: 2rem;\n            }\n            \n            .section-title {\n                font-size: 1.5rem;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header>\n            <h1><i class=\"fas fa-robot\"></i> AI传术师俱乐部 | 生财有术</h1>\n            <p class=\"subtitle\">2025年6月19日聊天数据分析报告</p>\n        </header>\n        \n        <div class=\"summary-cards\">\n            <div class=\"card\">\n                <div class=\"card-title\">\n                    <i class=\"fas fa-comments\"></i>\n                    <span>消息总数</span>\n                </div>\n                <div class=\"card-content\">326 <small>有效文本消息: 277</small></div>\n            </div>\n            \n            <div class=\"card\">\n                <div class=\"card-title\">\n                    <i class=\"fas fa-users\"></i>\n                    <span>活跃用户</span>\n                </div>\n                <div class=\"card-content\">70</div>\n            </div>\n            \n            <div class=\"card\">\n                <div class=\"card-title\">\n                    <i class=\"fas fa-clock\"></i>\n                    <span>时间范围</span>\n                </div>\n                <div class=\"card-content\">09:07 - 23:32</div>\n            </div>\n            \n            <div class=\"card\">\n                <div class=\"card-title\">\n                    <i class=\"fas fa-star\"></i>\n                    <span>主要发言者</span>\n                </div>\n                <div class=\"card-content\">\n                    饼干哥哥🍪AGI(49)\n                    <small>七天可爱多(30) 二进制刀仔(21)</small>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"section-title\">\n            <i class=\"fas fa-key\"></i>\n            <span>核心关键词速览</span>\n        </div>\n        <div class=\"chart-container\">\n            <span class=\"keyword-tag\">AI视频工具</span>\n            <span class=\"keyword-tag\">生财龙珠</span>\n            <span class=\"keyword-tag\">传术师</span>\n            <span class=\"keyword-tag\">豆包模型</span>\n            <span class=\"keyword-tag\">工作流</span>\n            <span class=\"keyword-tag\">即梦工具</span>\n            <span class=\"keyword-tag\">特级传术师</span>\n            <span class=\"keyword-tag\">社群规范</span>\n            <span class=\"keyword-tag\">视频生成</span>\n            <span class=\"keyword-tag\">中视频制作</span>\n        </div>\n        \n        <div class=\"section-title\">\n            <i class=\"fas fa-project-diagram\"></i>\n            <span>核心概念关系图</span>\n        </div>\n        <div class=\"mermaid-container\">\n            <div class=\"mermaid\">\n                %%{init: {'theme': 'base', 'themeVariables': {'primaryColor': '#FFD54F', 'nodeBorder': '#FFA726', 'lineColor': '#FF7043', 'textColor': '#5D4037'}}}%%\n                flowchart LR\n                A[AI视频工具] --> B[豆包模型]\n                A --> C[即梦工具]\n                A --> D[海螺视频]\n                A --> E[MJ视频]\n                F[特级传术师] --> G[云舒]\n                F --> H[龙珠奖励]\n                H --> I[IP打造服务]\n                H --> J[生财合作]\n                K[社群规范] --> L[入群门槛]\n                K --> M[退群机制]\n                L --> N[精华帖]\n                L --> O[社群分享]\n                P[工作流] --> Q[视频生成]\n                P --> R[背景音乐]\n                P --> S[配音]\n                Q --> T[中视频制作]\n                T --> U[YouTube收益]\n            </div>\n        </div>\n        \n        <div class=\"section-title\">\n            <i class=\"fas fa-comment-dots\"></i>\n            <span>精华话题聚焦</span>\n        </div>\n        <div class=\"grid\">\n            <div class=\"topic-card\">\n                <div class=\"topic-header\">\n                    <i class=\"fas fa-video\"></i>\n                    <h3>AI视频工具链讨论</h3>\n                </div>\n                <div class=\"topic-content\">\n                    <p>深入探讨了AI视频生成工具链的选择与优化，涵盖了从文本生成、分镜设计、图像生成到视频合成的全流程解决方案。</p>\n                </div>\n                <h4>重要对话节选</h4>\n                <div class=\"message\">\n                    <div class=\"message-header\">\n                        <span>饼干哥哥🍪AGI</span>\n                        <span>14:36</span>\n                    </div>\n                    <p>请教一下大佬，现在ai工具层出不穷，想问下制作一个5分钟的ai中视频：生成画面、分镜视频、背景音乐、配音，用什么工具性价比最高，效果也OK呢？</p>\n                </div>\n                <div class=\"message\">\n                    <div class=\"message-header\">\n                        <span>二进制刀仔</span>\n                        <span>14:43</span>\n                    </div>\n                    <p>文字直出可控性太低，我这个工作流就全用的豆包模型</p>\n                </div>\n                <div class=\"message\">\n                    <div class=\"message-header\">\n                        <span>阿超</span>\n                        <span>14:46</span>\n                    </div>\n                    <p>图生视频如果对质量要求不高，智谱真的量大管饱，咸鱼买个年度会员才五六十，可以无限生成</p>\n                </div>\n            </div>\n            \n            <div class=\"topic-card\">\n                <div class=\"topic-header\">\n                    <i class=\"fas fa-trophy\"></i>\n                    <h3>特级传术师成就</h3>\n                </div>\n                <div class=\"topic-content\">\n                    <p>宣布云舒成为生财有术第九年首位特级传术师，引发社群热烈祝贺并讨论龙珠奖励体系的价值与获取方式。</p>\n                </div>\n                <h4>重要对话节选</h4>\n                <div class=\"message\">\n                    <div class=\"message-header\">\n                        <span>七天可爱多</span>\n                        <span>13:36</span>\n                    </div>\n                    <p>第三个好消息就是 就在昨天的龙珠月报中~ 我们群诞生了第一位特级传术师 大家猜猜是谁</p>\n                </div>\n                <div class=\"message\">\n                    <div class=\"message-header\">\n                        <span>Gavin 吴佳文</span>\n                        <span>13:37</span>\n                    </div>\n                    <p>我猜是云舒</p>\n                </div>\n                <div class=\"message\">\n                    <div class=\"message-header\">\n                        <span>七天可爱多</span>\n                        <span>13:39</span>\n                    </div>\n                    <p>哈哈哈 揭秘！大家猜的没错~ 恭喜本群在生财第9年的第一个特级传术师：@云舒</p>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"section-title\">\n            <i class=\"fas fa-star\"></i>\n            <span>群友金句闪耀</span>\n        </div>\n        <div class=\"grid\">\n            <div class=\"quote-card\">\n                <p class=\"quote-text\">\"数码类的<span class=\"quote-highlight\">女生做普遍比男生更好</span>，尤其是新品能拿到一手资源\"</p>\n                <p class=\"quote-author\">—— 阿超 · 09:38</p>\n            </div>\n            \n            <div class=\"quote-card\">\n                <p class=\"quote-text\">\"本来想报油管航海的，靠这个回本，后来发现错了，应该<span class=\"quote-highlight\">先找对标，再搭工作流</span>\"</p>\n                <p class=\"quote-author\">—— 二进制刀仔 · 14:49</p>\n            </div>\n            \n            <div class=\"quote-card\">\n                <p class=\"quote-text\">\"在生财有术，<span class=\"quote-highlight\">龙珠是用来感谢那些为生财做出重要贡献的圈友</span>\"</p>\n                <p class=\"quote-author\">—— 七天可爱多 · 13:44</p>\n            </div>\n            \n            <div class=\"quote-card\">\n                <p class=\"quote-text\">\"创作有回响，分享被看见。欢迎一起，用一篇篇帖子，让更多人<span class=\"quote-highlight\">看到、变好、赚到</span>\"</p>\n                <p class=\"quote-author\">—— 七天可爱多 · 13:45</p>\n            </div>\n        </div>\n        \n        <div class=\"section-title\">\n            <i class=\"fas fa-chart-bar\"></i>\n            <span>发言数据可视化</span>\n        </div>\n        <div class=\"grid\">\n            <div class=\"chart-container\">\n                <canvas id=\"userChart\"></canvas>\n            </div>\n            <div class=\"chart-container\">\n                <canvas id=\"hourlyChart\"></canvas>\n            </div>\n        </div>\n    </div>\n\n    <script>\n        // 初始化Mermaid\n        mermaid.initialize({\n            startOnLoad: true,\n            theme: 'base',\n            themeVariables: {\n                primaryColor: '#FFD54F',\n                nodeBorder: '#FFA726',\n                lineColor: '#FF7043',\n                textColor: '#5D4037'\n            }\n        });\n        \n        // 用户发言数据\n        const userData = {\n            labels: ['饼干哥哥🍪AGI', '七天可爱多', '二进制刀仔', 'Gavin 吴佳文', '阿超'],\n            datasets: [{\n                label: '发言数量',\n                data: [49, 30, 21, 15, 13],\n                backgroundColor: [\n                    'rgba(255, 167, 38, 0.7)',\n                    'rgba(255, 213, 79, 0.7)',\n                    'rgba(255, 112, 67, 0.7)',\n                    'rgba(255, 183, 77, 0.7)',\n                    'rgba(255, 152, 0, 0.7)'\n                ],\n                borderColor: [\n                    'rgba(255, 167, 38, 1)',\n                    'rgba(255, 213, 79, 1)',\n                    'rgba(255, 112, 67, 1)',\n                    'rgba(255, 183, 77, 1)',\n                    'rgba(255, 152, 0, 1)'\n                ],\n                borderWidth: 1\n            }]\n        };\n        \n        // 时间分布数据\n        const hourlyData = {\n            labels: ['9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'],\n            datasets: [{\n                label: '消息数量',\n                data: [28, 15, 8, 12, 68, 32, 45, 22, 18, 24, 16, 10, 15, 8, 5],\n                fill: true,\n                backgroundColor: 'rgba(255, 167, 38, 0.2)',\n                borderColor: 'rgba(255, 167, 38, 1)',\n                tension: 0.3,\n                pointBackgroundColor: 'rgba(255, 112, 67, 1)'\n            }]\n        };\n        \n        // 渲染图表\n        window.onload = function() {\n            const userCtx = document.getElementById('userChart').getContext('2d');\n            new Chart(userCtx, {\n                type: 'bar',\n                data: userData,\n                options: {\n                    responsive: true,\n                    plugins: {\n                        legend: {\n                            display: false\n                        },\n                        title: {\n                            display: true,\n                            text: 'TOP 5 活跃用户发言统计',\n                            font: {\n                                size: 18\n                            }\n                        }\n                    },\n                    scales: {\n                        y: {\n                            beginAtZero: true,\n                            ticks: {\n                                precision: 0\n                            }\n                        }\n                    }\n                }\n            });\n            \n            const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');\n            new Chart(hourlyCtx, {\n                type: 'line',\n                data: hourlyData,\n                options: {\n                    responsive: true,\n                    plugins: {\n                        title: {\n                            display: true,\n                            text: '每小时消息数量分布',\n                            font: {\n                                size: 18\n                            }\n                        }\n                    },\n                    scales: {\n                        y: {\n                            beginAtZero: true,\n                            ticks: {\n                                precision: 0\n                            }\n                        }\n                    }\n                }\n            });\n        };\n    </script>\n</body>\n</html>", "savedAt": "2025-06-19T16:58:41.382Z"}