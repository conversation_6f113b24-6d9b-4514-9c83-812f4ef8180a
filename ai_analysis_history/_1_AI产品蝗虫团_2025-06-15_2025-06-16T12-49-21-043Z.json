{"groupName": "【1】AI产品蝗虫团", "analysisType": "custom", "timeRange": "2025-06-15", "messageCount": 92, "timestamp": "2025-06-16T12:49:21.043Z", "title": "【1】AI产品蝗虫团 - 自定义分析", "content": "```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>【1】AI产品蝗虫团 - 2025年6月15日聊天分析报告</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/moment\"></script>\n    <style>\n        :root {\n            --primary: #FF7E5F;\n            --secondary: #FEB47B;\n            --accent: #FF6B6B;\n            --light: #FFF5E6;\n            --dark: #5C4033;\n            --text: #5C4033;\n            --text-light: #8B7355;\n        }\n        \n        body {\n            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;\n            background-color: var(--light);\n            color: var(--text);\n            line-height: 1.6;\n            margin: 0;\n            padding: 0;\n        }\n        \n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n            padding: 20px;\n        }\n        \n        header {\n            background: linear-gradient(135deg, var(--primary), var(--secondary));\n            color: white;\n            padding: 30px 20px;\n            border-radius: 10px;\n            margin-bottom: 30px;\n            box-shadow: 0 4px 15px rgba(0,0,0,0.1);\n        }\n        \n        h1 {\n            margin: 0;\n            font-size: 2.2rem;\n        }\n        \n        h2 {\n            color: var(--primary);\n            border-bottom: 2px solid var(--secondary);\n            padding-bottom: 8px;\n            margin-top: 40px;\n        }\n        \n        h3 {\n            color: var(--accent);\n        }\n        \n        .card {\n            background-color: white;\n            border-radius: 10px;\n            padding: 20px;\n            margin-bottom: 20px;\n            box-shadow: 0 4px 8px rgba(0,0,0,0.05);\n            transition: transform 0.3s ease;\n        }\n        \n        .card:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 6px 12px rgba(0,0,0,0.1);\n        }\n        \n        .keyword-tag {\n            display: inline-block;\n            background-color: var(--secondary);\n            color: white;\n            padding: 5px 12px;\n            border-radius: 20px;\n            margin: 5px;\n            font-size: 0.9rem;\n            font-weight: 500;\n        }\n        \n        .message {\n            margin: 15px 0;\n            padding: 15px;\n            border-radius: 10px;\n            background-color: #FFF9F2;\n            border-left: 4px solid var(--primary);\n        }\n        \n        .message-header {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 8px;\n            color: var(--text-light);\n            font-size: 0.9rem;\n        }\n        \n        .message-user {\n            font-weight: bold;\n            color: var(--primary);\n        }\n        \n        .quote {\n            font-style: italic;\n            background-color: #FFF0E0;\n            padding: 15px;\n            border-radius: 10px;\n            position: relative;\n            margin: 20px 0;\n        }\n        \n        .quote:before {\n            content: '\"';\n            font-size: 4rem;\n            color: rgba(255, 126, 95, 0.1);\n            position: absolute;\n            top: -20px;\n            left: 5px;\n        }\n        \n        .quote-author {\n            text-align: right;\n            font-weight: bold;\n            color: var(--accent);\n            margin-top: 10px;\n        }\n        \n        .grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n            gap: 20px;\n        }\n        \n        .product-item {\n            padding: 10px;\n            border-bottom: 1px dashed #FFD8C2;\n        }\n        \n        .product-name {\n            font-weight: bold;\n            color: var(--primary);\n        }\n        \n        .stats-card {\n            text-align: center;\n            padding: 20px;\n        }\n        \n        .stats-number {\n            font-size: 2.5rem;\n            font-weight: bold;\n            color: var(--primary);\n            margin: 10px 0;\n        }\n        \n        .stats-label {\n            color: var(--text-light);\n        }\n        \n        @media (max-width: 768px) {\n            .grid {\n                grid-template-columns: 1fr;\n            }\n            \n            h1 {\n                font-size: 1.8rem;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header>\n            <h1>【1】AI产品蝗虫团 - 2025年6月15日聊天分析报告</h1>\n            <p>深入分析群聊中的AI产品讨论与技术交流</p>\n        </header>\n        \n        <section>\n            <h2>聊天数据概览</h2>\n            <div class=\"grid\">\n                <div class=\"card stats-card\">\n                    <div class=\"stats-number\">92</div>\n                    <div class=\"stats-label\">总消息数</div>\n                </div>\n                <div class=\"card stats-card\">\n                    <div class=\"stats-number\">16</div>\n                    <div class=\"stats-label\">活跃用户数</div>\n                </div>\n                <div class=\"card stats-card\">\n                    <div class=\"stats-number\">3</div>\n                    <div class=\"stats-label\">讨论小时数</div>\n                </div>\n            </div>\n        </section>\n        \n        <section>\n            <h2>核心关键词</h2>\n            <div class=\"card\">\n                <span class=\"keyword-tag\">ComfyUI</span>\n                <span class=\"keyword-tag\">Lora</span>\n                <span class=\"keyword-tag\">Labubu</span>\n                <span class=\"keyword-tag\">星流agent</span>\n                <span class=\"keyword-tag\">豆包</span>\n                <span class=\"keyword-tag\">veo</span>\n                <span class=\"keyword-tag\">liblib</span>\n                <span class=\"keyword-tag\">Runninghub</span>\n            </div>\n        </section>\n        \n        <section>\n            <h2>活跃用户排行</h2>\n            <div class=\"card\">\n                <canvas id=\"userChart\" height=\"300\"></canvas>\n            </div>\n        </section>\n        \n        <section>\n            <h2>热门话题</h2>\n            \n            <div class=\"card\">\n                <h3>ComfyUI与Lora技术讨论</h3>\n                <p>群内围绕ComfyUI的使用体验、Lora技术的应用场景进行了深入讨论，分享了多个实用工作流和资源链接。</p>\n                \n                <div class=\"message\">\n                    <div class=\"message-header\">\n                        <span class=\"message-user\">Shaun</span>\n                        <span>20:32:48</span>\n                    </div>\n                    <p>Comfyui一周基本文生图这块拿捏了，工作流+lora+api 太能打了，接个api 用midjourney konext等也很方便</p>\n                </div>\n                \n                <div class=\"message\">\n                    <div class=\"message-header\">\n                        <span class=\"message-user\">Shaun</span>\n                        <span>20:35:22</span>\n                    </div>\n                    <p>对 Lora是入门</p>\n                </div>\n                \n                <div class=\"message\">\n                    <div class=\"message-header\">\n                        <span class=\"message-user\">Shaun</span>\n                        <span>20:35:57</span>\n                    </div>\n                    <p>liblib训练个lora，拿去Runninghub用 哈哈哈哈</p>\n                </div>\n            </div>\n            \n            <div class=\"card\">\n                <h3>星流agent体验分享</h3>\n                <p>多位群友分享了使用星流agent的体验，讨论了其优缺点和潜在应用场景。</p>\n                \n                <div class=\"message\">\n                    <div class=\"message-header\">\n                        <span class=\"message-user\">Josh</span>\n                        <span>20:46:05</span>\n                    </div>\n                    <p>星流体验确实不错</p>\n                </div>\n                \n                <div class=\"message\">\n                    <div class=\"message-header\">\n                        <span class=\"message-user\">Josh</span>\n                        <span>20:46:13</span>\n                    </div>\n                    <p>我这几天也一直在用</p>\n                </div>\n                \n                <div class=\"message\">\n                    <div class=\"message-header\">\n                        <span class=\"message-user\">Josh</span>\n                        <span>20:47:08</span>\n                    </div>\n                    <p>还可以上传图来参考姿势和样子。</p>\n                </div>\n            </div>\n        </section>\n        \n        <section>\n            <h2>精彩语录</h2>\n            \n            <div class=\"quote\">\n                \"Comfyui一周基本文生图这块拿捏了，工作流+lora+api 太能打了\"\n                <div class=\"quote-author\">— Shaun, 20:32:48</div>\n            </div>\n            \n            <div class=\"quote\">\n                \"星流体验确实不错，我这几天也一直在用\"\n                <div class=\"quote-author\">— Josh, 20:46:13</div>\n            </div>\n            \n            <div class=\"quote\">\n                \"豆包的最新模型已经超过veo3了，就是没声音\"\n                <div class=\"quote-author\">— 嘉琛, 23:16:12</div>\n            </div>\n        </section>\n        \n        <section>\n            <h2>提及的产品与资源</h2>\n            <div class=\"card\">\n                <div class=\"product-item\">\n                    <span class=\"product-name\">ComfyUI</span> - 强大的AI图像生成工具，支持工作流定制和API集成\n                </div>\n                <div class=\"product-item\">\n                    <span class=\"product-name\">Lora</span> - 用于AI模型微调的技术，可提高生成结果的一致性\n                </div>\n                <div class=\"product-item\">\n                    <span class=\"product-name\">星流agent</span> - 支持图像参考的AI生成工具\n                </div>\n                <div class=\"product-item\">\n                    <span class=\"product-name\">豆包</span> - 视频生成模型，效果被认为超过veo3\n                </div>\n                <div class=\"product-item\">\n                    <span class=\"product-name\">liblib</span> - Lora训练平台\n                </div>\n                <div class=\"product-item\">\n                    <span class=\"product-name\">Runninghub</span> - 相对便宜的AI模型运行平台\n                </div>\n            </div>\n        </section>\n        \n        <section>\n            <h2>消息时间分布</h2>\n            <div class=\"card\">\n                <canvas id=\"timeChart\" height=\"300\"></canvas>\n            </div>\n        </section>\n    </div>\n\n    <script>\n        // 活跃用户数据\n        const userData = {\n            labels: ['嘉琛', 'Shaun', '🇨🇳', 'Josh', 'Liu、', '其他'],\n            datasets: [{\n                label: '发言数量',\n                data: [16, 15, 11, 8, 4, 38],\n                backgroundColor: [\n                    '#FF7E5F',\n                    '#FEB47B',\n                    '#FF6B6B',\n                    '#FFA07A',\n                    '#FFD700',\n                    '#8B7355'\n                ],\n                borderWidth: 1\n            }]\n        };\n        \n        // 时间分布数据\n        const timeLabels = ['20:00', '21:00', '22:00', '23:00'];\n        const timeData = [35, 25, 20, 12];\n        \n        // 初始化用户图表\n        const userCtx = document.getElementById('userChart').getContext('2d');\n        new Chart(userCtx, {\n            type: 'bar',\n            data: userData,\n            options: {\n                responsive: true,\n                plugins: {\n                    legend: {\n                        display: false\n                    }\n                },\n                scales: {\n                    y: {\n                        beginAtZero: true\n                    }\n                }\n            }\n        });\n        \n        // 初始化时间图表\n        const timeCtx = document.getElementById('timeChart').getContext('2d');\n        new Chart(timeCtx, {\n            type: 'line',\n            data: {\n                labels: timeLabels,\n                datasets: [{\n                    label: '消息数量',\n                    data: timeData,\n                    borderColor: '#FF7E5F',\n                    backgroundColor: 'rgba(255, 126, 95, 0.1)',\n                    fill: true,\n                    tension: 0.4\n                }]\n            },\n            options: {\n                responsive: true,\n                plugins: {\n                    legend: {\n                        display: false\n                    }\n                },\n                scales: {\n                    y: {\n                        beginAtZero: true\n                    }\n                }\n            }\n        });\n    </script>\n</body>\n</html>\n```", "savedAt": "2025-06-16T12:49:21.043Z"}