<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .config-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 10px 0; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>AI智能分析中心配置测试</h1>
    
    <button onclick="testConfig()">测试配置加载</button>
    <button onclick="testGemini()">测试Gemini API</button>
    <button onclick="clearCache()">清除缓存</button>
    
    <div id="results"></div>

    <script>
        async function testConfig() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>正在测试配置...</p>';
            
            try {
                const response = await fetch('/api/get-analysis-config');
                const data = await response.json();
                
                if (data.success && data.config && data.config.dynamicAnalysisItems) {
                    const items = data.config.dynamicAnalysisItems;
                    let html = `<div class="config-item success">
                        <h3>✅ 配置加载成功</h3>
                        <p>找到 ${items.length} 个动态分析项：</p>
                        <ul>`;
                    
                    items.forEach(item => {
                        html += `<li><strong>${item.displayName}</strong> (${item.groupName})</li>`;
                    });
                    
                    html += `</ul>
                        <h4>完整配置：</h4>
                        <pre>${JSON.stringify(data.config, null, 2)}</pre>
                    </div>`;
                    
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="config-item error">
                        <h3>❌ 配置加载失败</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="config-item error">
                    <h3>❌ 请求失败</h3>
                    <p>${error.message}</p>
                </div>`;
            }
        }
        
        async function testGemini() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>正在测试Gemini API...</p>';
            
            try {
                const response = await fetch('/api/model-settings/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        provider: 'Gemini',
                        config: {
                            model: 'gemini-2.5-pro',
                            apiKey: 'AIzaSyCFj4Yu3oDRR6q6xPrPz4wk3az9b6t61JE'
                        }
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultsDiv.innerHTML = `<div class="config-item success">
                        <h3>✅ Gemini API测试成功</h3>
                        <p><strong>模型:</strong> ${data.model}</p>
                        <p><strong>响应:</strong> ${data.response}</p>
                    </div>`;
                } else {
                    resultsDiv.innerHTML = `<div class="config-item error">
                        <h3>❌ Gemini API测试失败</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="config-item error">
                    <h3>❌ 请求失败</h3>
                    <p>${error.message}</p>
                </div>`;
            }
        }
        
        function clearCache() {
            localStorage.removeItem('dynamicAnalysisItems');
            localStorage.removeItem('aiAnalysisSettings');
            alert('缓存已清除！请刷新主页面查看效果。');
        }
        
        // 页面加载时自动测试配置
        window.onload = function() {
            testConfig();
        };
    </script>
</body>
</html>
