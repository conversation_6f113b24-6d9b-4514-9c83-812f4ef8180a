<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; max-height: 300px; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🐛 配置调试页面</h1>
    
    <div class="debug-section">
        <h3>1. 服务器配置测试</h3>
        <button onclick="testServerConfig()">测试服务器配置</button>
        <div id="serverConfigResult"></div>
    </div>

    <div class="debug-section">
        <h3>2. 前端localStorage检查</h3>
        <button onclick="checkLocalStorage()">检查本地存储</button>
        <button onclick="clearLocalStorage()">清除本地存储</button>
        <div id="localStorageResult"></div>
    </div>

    <div class="debug-section">
        <h3>3. 强制重新加载配置</h3>
        <button onclick="forceReloadConfig()">强制重新加载</button>
        <div id="reloadResult"></div>
    </div>

    <div class="debug-section">
        <h3>4. 模拟前端加载过程</h3>
        <button onclick="simulateFrontendLoad()">模拟前端加载</button>
        <div id="simulateResult"></div>
    </div>

    <div class="debug-section">
        <h3>5. 操作日志</h3>
        <button onclick="clearLog()">清除日志</button>
        <div id="debugLog" class="log"></div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        async function testServerConfig() {
            const resultDiv = document.getElementById('serverConfigResult');
            log('🔄 开始测试服务器配置...');
            
            try {
                const response = await fetch('/api/get-analysis-config');
                const data = await response.json();
                
                if (data.success && data.config && data.config.dynamicAnalysisItems) {
                    const items = data.config.dynamicAnalysisItems;
                    log(`✅ 服务器配置正常，找到 ${items.length} 个分析项`);
                    
                    let html = `<div class="success">
                        <h4>✅ 服务器配置正常</h4>
                        <p>找到 ${items.length} 个动态分析项：</p>
                        <ul>`;
                    
                    items.forEach((item, index) => {
                        html += `<li>${index + 1}. ${item.displayName} (${item.groupName})</li>`;
                        log(`  ${index + 1}. ${item.displayName}`);
                    });
                    
                    html += `</ul></div>`;
                    resultDiv.innerHTML = html;
                } else {
                    log('❌ 服务器配置格式错误');
                    resultDiv.innerHTML = `<div class="error">
                        <h4>❌ 服务器配置错误</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>`;
                }
            } catch (error) {
                log(`❌ 服务器配置测试失败: ${error.message}`);
                resultDiv.innerHTML = `<div class="error">
                    <h4>❌ 请求失败</h4>
                    <p>${error.message}</p>
                </div>`;
            }
        }

        function checkLocalStorage() {
            const resultDiv = document.getElementById('localStorageResult');
            log('🔄 检查本地存储...');
            
            const dynamicItems = localStorage.getItem('dynamicAnalysisItems');
            const aiSettings = localStorage.getItem('aiAnalysisSettings');
            
            let html = '<div class="warning"><h4>📦 本地存储状态</h4>';
            
            if (dynamicItems) {
                try {
                    const items = JSON.parse(dynamicItems);
                    html += `<p><strong>dynamicAnalysisItems:</strong> ${items.length} 个项目</p>`;
                    log(`📦 本地存储中有 ${items.length} 个动态分析项`);
                } catch (e) {
                    html += `<p><strong>dynamicAnalysisItems:</strong> 解析错误</p>`;
                    log('❌ dynamicAnalysisItems 解析错误');
                }
            } else {
                html += `<p><strong>dynamicAnalysisItems:</strong> 空</p>`;
                log('📦 dynamicAnalysisItems 为空');
            }
            
            if (aiSettings) {
                html += `<p><strong>aiAnalysisSettings:</strong> 已存在</p>`;
                log('📦 aiAnalysisSettings 已存在');
            } else {
                html += `<p><strong>aiAnalysisSettings:</strong> 空</p>`;
                log('📦 aiAnalysisSettings 为空');
            }
            
            html += '</div>';
            resultDiv.innerHTML = html;
        }

        function clearLocalStorage() {
            log('🗑️ 清除本地存储...');
            localStorage.removeItem('dynamicAnalysisItems');
            localStorage.removeItem('aiAnalysisSettings');
            localStorage.removeItem('analysisHistory');
            log('✅ 本地存储已清除');
            checkLocalStorage();
        }

        async function forceReloadConfig() {
            const resultDiv = document.getElementById('reloadResult');
            log('🔄 强制重新加载配置...');
            
            try {
                // 清除本地存储
                clearLocalStorage();
                
                // 重新从服务器加载
                const response = await fetch('/api/get-analysis-config');
                const data = await response.json();
                
                if (data.success && data.config && data.config.dynamicAnalysisItems) {
                    // 保存到本地存储
                    localStorage.setItem('dynamicAnalysisItems', JSON.stringify(data.config.dynamicAnalysisItems));
                    
                    log(`✅ 配置重新加载成功，保存了 ${data.config.dynamicAnalysisItems.length} 个分析项`);
                    resultDiv.innerHTML = `<div class="success">
                        <h4>✅ 配置重新加载成功</h4>
                        <p>已保存 ${data.config.dynamicAnalysisItems.length} 个分析项到本地存储</p>
                        <p><strong>建议：</strong>现在刷新主页面查看效果</p>
                    </div>`;
                } else {
                    log('❌ 配置重新加载失败');
                    resultDiv.innerHTML = `<div class="error">
                        <h4>❌ 配置重新加载失败</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>`;
                }
            } catch (error) {
                log(`❌ 强制重新加载失败: ${error.message}`);
                resultDiv.innerHTML = `<div class="error">
                    <h4>❌ 操作失败</h4>
                    <p>${error.message}</p>
                </div>`;
            }
        }

        async function simulateFrontendLoad() {
            const resultDiv = document.getElementById('simulateResult');
            log('🔄 模拟前端加载过程...');
            
            try {
                // 步骤1：清除缓存
                log('步骤1: 清除本地缓存');
                localStorage.removeItem('dynamicAnalysisItems');
                localStorage.removeItem('aiAnalysisSettings');
                
                // 步骤2：从服务器加载配置
                log('步骤2: 从服务器加载配置');
                const response = await fetch('/api/get-analysis-config');
                const data = await response.json();
                
                if (!data.success || !data.config || !data.config.dynamicAnalysisItems) {
                    throw new Error('服务器配置格式错误');
                }
                
                // 步骤3：保存到本地存储
                log('步骤3: 保存到本地存储');
                localStorage.setItem('dynamicAnalysisItems', JSON.stringify(data.config.dynamicAnalysisItems));
                
                // 步骤4：验证保存结果
                log('步骤4: 验证保存结果');
                const saved = localStorage.getItem('dynamicAnalysisItems');
                const parsedItems = JSON.parse(saved);
                
                log(`✅ 模拟加载完成，共 ${parsedItems.length} 个分析项`);
                
                let html = `<div class="success">
                    <h4>✅ 模拟前端加载成功</h4>
                    <p>成功加载并保存 ${parsedItems.length} 个分析项</p>
                    <h5>分析项列表：</h5>
                    <ul>`;
                
                parsedItems.forEach((item, index) => {
                    html += `<li>${index + 1}. ${item.displayName}</li>`;
                });
                
                html += `</ul>
                    <p><strong>下一步：</strong>在主页面中应该能看到这些分析项</p>
                </div>`;
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                log(`❌ 模拟加载失败: ${error.message}`);
                resultDiv.innerHTML = `<div class="error">
                    <h4>❌ 模拟加载失败</h4>
                    <p>${error.message}</p>
                </div>`;
            }
        }

        // 页面加载时自动检查
        window.onload = function() {
            log('🚀 调试页面已加载');
            testServerConfig();
            checkLocalStorage();
        };
    </script>
</body>
</html>
