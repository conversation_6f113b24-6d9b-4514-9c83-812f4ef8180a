<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>群聊排序测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; border-left: 4px solid #28a745; }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        .info { background-color: #d1ecf1; border-left: 4px solid #17a2b8; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; max-height: 400px; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .chatroom-item { background: #f8f9fa; margin: 5px 0; padding: 10px; border-radius: 4px; border-left: 4px solid #007bff; display: flex; justify-content: space-between; align-items: center; }
        .chatroom-name { font-weight: bold; }
        .chatroom-count { background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; }
        .top-chatrooms { max-height: 300px; overflow-y: auto; }
        .search-box { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #007bff; }
        .stat-label { font-size: 14px; color: #666; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 群聊排序测试页面</h1>
        
        <div class="test-section">
            <h3>📈 群聊统计信息</h3>
            <button onclick="loadChatroomStats()">加载群聊统计</button>
            <div id="chatroomStats"></div>
        </div>

        <div class="test-section">
            <h3>🔝 成员最多的群聊 (前20名)</h3>
            <button onclick="loadTopChatrooms()">加载排行榜</button>
            <div id="topChatrooms"></div>
        </div>

        <div class="test-section">
            <h3>🔍 群聊搜索测试</h3>
            <input type="text" id="searchInput" class="search-box" placeholder="输入群聊名称搜索..." oninput="searchChatrooms()">
            <div id="searchResults"></div>
        </div>

        <div class="test-section">
            <h3>🧪 排序算法测试</h3>
            <button onclick="testSortingAlgorithm()">测试排序算法</button>
            <div id="sortingTest"></div>
        </div>

        <div class="test-section">
            <h3>📝 操作日志</h3>
            <button onclick="clearLog()">清除日志</button>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <script>
        let allChatrooms = [];

        function log(message) {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }

        async function loadChatroomStats() {
            const resultDiv = document.getElementById('chatroomStats');
            log('📊 开始加载群聊统计信息...');
            
            try {
                const response = await fetch('/api/chatrooms');
                const data = await response.json();
                
                if (response.ok) {
                    allChatrooms = data;
                    
                    // 计算统计信息
                    const totalChatrooms = data.length;
                    const userCounts = data.map(room => parseInt(room.userCount) || 0);
                    const totalMembers = userCounts.reduce((sum, count) => sum + count, 0);
                    const avgMembers = totalMembers / totalChatrooms;
                    const maxMembers = Math.max(...userCounts);
                    const minMembers = Math.min(...userCounts);
                    
                    // 按人数分组
                    const groups = {
                        large: userCounts.filter(count => count >= 100).length,
                        medium: userCounts.filter(count => count >= 20 && count < 100).length,
                        small: userCounts.filter(count => count < 20).length
                    };
                    
                    const html = `
                        <div class="stats">
                            <div class="stat-card">
                                <div class="stat-number">${totalChatrooms}</div>
                                <div class="stat-label">总群聊数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${totalMembers.toLocaleString()}</div>
                                <div class="stat-label">总成员数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${avgMembers.toFixed(1)}</div>
                                <div class="stat-label">平均成员数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${maxMembers}</div>
                                <div class="stat-label">最大群聊</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${groups.large}</div>
                                <div class="stat-label">大群 (≥100人)</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${groups.medium}</div>
                                <div class="stat-label">中群 (20-99人)</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${groups.small}</div>
                                <div class="stat-label">小群 (<20人)</div>
                            </div>
                        </div>
                    `;
                    
                    resultDiv.innerHTML = html;
                    log(`✅ 统计完成: ${totalChatrooms} 个群聊，总计 ${totalMembers} 人`);
                } else {
                    throw new Error('获取群聊列表失败');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
                log(`❌ 加载群聊统计失败: ${error.message}`);
            }
        }

        async function loadTopChatrooms() {
            const resultDiv = document.getElementById('topChatrooms');
            log('🔝 开始加载成员最多的群聊...');
            
            try {
                if (allChatrooms.length === 0) {
                    const response = await fetch('/api/chatrooms');
                    allChatrooms = await response.json();
                }
                
                // 按成员人数降序排序
                const sortedChatrooms = allChatrooms
                    .map(room => ({
                        ...room,
                        userCountNum: parseInt(room.userCount) || 0
                    }))
                    .sort((a, b) => {
                        const countDiff = b.userCountNum - a.userCountNum;
                        if (countDiff !== 0) return countDiff;
                        return a.displayName.localeCompare(b.displayName);
                    })
                    .slice(0, 20);
                
                let html = '<div class="top-chatrooms">';
                sortedChatrooms.forEach((room, index) => {
                    html += `
                        <div class="chatroom-item">
                            <div>
                                <span style="color: #666; margin-right: 10px;">#${index + 1}</span>
                                <span class="chatroom-name">${room.displayName}</span>
                            </div>
                            <span class="chatroom-count">${room.userCount}人</span>
                        </div>
                    `;
                });
                html += '</div>';
                
                resultDiv.innerHTML = html;
                log(`✅ 显示了前20个成员最多的群聊，最大群聊: ${sortedChatrooms[0]?.displayName} (${sortedChatrooms[0]?.userCount}人)`);
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
                log(`❌ 加载排行榜失败: ${error.message}`);
            }
        }

        function searchChatrooms() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
            const resultDiv = document.getElementById('searchResults');
            
            if (!searchTerm) {
                resultDiv.innerHTML = '';
                return;
            }
            
            if (allChatrooms.length === 0) {
                resultDiv.innerHTML = '<div class="warning">⚠️ 请先加载群聊统计信息</div>';
                return;
            }
            
            log(`🔍 搜索群聊: "${searchTerm}"`);
            
            // 搜索并排序
            const filteredChatrooms = allChatrooms
                .filter(room => room.displayName.toLowerCase().includes(searchTerm))
                .map(room => ({
                    ...room,
                    userCountNum: parseInt(room.userCount) || 0
                }))
                .sort((a, b) => {
                    const countDiff = b.userCountNum - a.userCountNum;
                    if (countDiff !== 0) return countDiff;
                    return a.displayName.localeCompare(b.displayName);
                })
                .slice(0, 10);
            
            if (filteredChatrooms.length === 0) {
                resultDiv.innerHTML = '<div class="info">📭 未找到匹配的群聊</div>';
                return;
            }
            
            let html = `<h4>🔍 搜索结果 (${filteredChatrooms.length} 个群聊，按成员人数排序)</h4><div class="top-chatrooms">`;
            filteredChatrooms.forEach((room, index) => {
                html += `
                    <div class="chatroom-item">
                        <div>
                            <span style="color: #666; margin-right: 10px;">#${index + 1}</span>
                            <span class="chatroom-name">${room.displayName}</span>
                        </div>
                        <span class="chatroom-count">${room.userCount}人</span>
                    </div>
                `;
            });
            html += '</div>';
            
            resultDiv.innerHTML = html;
            log(`✅ 搜索完成，找到 ${filteredChatrooms.length} 个匹配的群聊`);
        }

        function testSortingAlgorithm() {
            const resultDiv = document.getElementById('sortingTest');
            log('🧪 开始测试排序算法...');
            
            if (allChatrooms.length === 0) {
                resultDiv.innerHTML = '<div class="warning">⚠️ 请先加载群聊统计信息</div>';
                return;
            }
            
            // 创建测试数据
            const testData = [
                { displayName: '测试群A', userCount: '50' },
                { displayName: '测试群B', userCount: '100' },
                { displayName: '测试群C', userCount: '50' },
                { displayName: '测试群D', userCount: '200' },
                { displayName: '测试群E', userCount: '10' }
            ];
            
            // 应用排序算法
            const sortedData = testData.sort((a, b) => {
                const countA = parseInt(a.userCount) || 0;
                const countB = parseInt(b.userCount) || 0;
                
                const countDiff = countB - countA;
                if (countDiff !== 0) return countDiff;
                
                return a.displayName.localeCompare(b.displayName);
            });
            
            let html = '<h4>🧪 排序算法测试结果</h4>';
            html += '<p><strong>原始数据:</strong> 测试群A(50), 测试群B(100), 测试群C(50), 测试群D(200), 测试群E(10)</p>';
            html += '<p><strong>排序结果:</strong></p><div class="top-chatrooms">';
            
            sortedData.forEach((room, index) => {
                html += `
                    <div class="chatroom-item">
                        <div>
                            <span style="color: #666; margin-right: 10px;">#${index + 1}</span>
                            <span class="chatroom-name">${room.displayName}</span>
                        </div>
                        <span class="chatroom-count">${room.userCount}人</span>
                    </div>
                `;
            });
            html += '</div>';
            
            // 验证排序是否正确
            const isCorrect = sortedData[0].displayName === '测试群D' && 
                             sortedData[1].displayName === '测试群B' &&
                             sortedData[2].displayName === '测试群A' &&
                             sortedData[3].displayName === '测试群C';
            
            html += `<div class="${isCorrect ? 'success' : 'error'}">
                ${isCorrect ? '✅ 排序算法正确' : '❌ 排序算法有误'}
            </div>`;
            
            resultDiv.innerHTML = html;
            log(`${isCorrect ? '✅' : '❌'} 排序算法测试${isCorrect ? '通过' : '失败'}`);
        }

        // 页面加载时自动加载统计信息
        window.onload = function() {
            log('🚀 群聊排序测试页面已加载');
            loadChatroomStats();
        };
    </script>
</body>
</html>
