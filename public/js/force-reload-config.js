// 强制重新加载配置的脚本
(function() {
    console.log('🔄 强制重新加载配置脚本已加载');

    // 清除所有相关的localStorage
    function clearAllCache() {
        const keysToRemove = [
            'dynamicAnalysisItems',
            'aiAnalysisSettings',
            'analysisHistory',
            'modelSettings'
        ];

        keysToRemove.forEach(key => {
            if (localStorage.getItem(key)) {
                console.log(`🗑️ 清除缓存: ${key}`);
                localStorage.removeItem(key);
            }
        });
    }

    // 强制从服务器重新加载配置
    async function forceReloadConfig() {
        try {
            console.log('🔄 开始强制重新加载配置...');

            // 清除缓存
            clearAllCache();

            // 从服务器获取最新配置
            const response = await fetch('/api/get-analysis-config');
            const data = await response.json();

            if (data.success && data.config && data.config.dynamicAnalysisItems) {
                console.log('📥 从服务器获取到配置:', data.config.dynamicAnalysisItems.length, '个分析项');

                // 直接保存到localStorage
                localStorage.setItem('dynamicAnalysisItems', JSON.stringify(data.config.dynamicAnalysisItems));

                // 重新初始化AI设置管理器
                if (window.aiSettingsManager) {
                    console.log('🔄 更新AI设置管理器...');
                    window.aiSettingsManager.dynamicAnalysisItems = data.config.dynamicAnalysisItems;
                    window.aiSettingsManager.saveDynamicItems();
                }

                // 重新加载动态分析项到页面
                if (window.chatlogApp && window.chatlogApp.loadDynamicAnalysisItems) {
                    console.log('🔄 重新加载页面UI...');
                    window.chatlogApp.loadDynamicAnalysisItems();
                }

                console.log('✅ 配置重新加载完成');
                return true;
            } else {
                console.error('❌ 服务器返回的配置格式不正确:', data);
                return false;
            }

        } catch (error) {
            console.error('❌ 强制重新加载配置失败:', error);
            return false;
        }
    }

    // 检查是否需要自动刷新
    function checkAndAutoRefresh() {
        setTimeout(() => {
            console.log('🔍 检查动态分析项状态...');

            // 检查localStorage中的配置
            const savedItems = localStorage.getItem('dynamicAnalysisItems');
            let itemCount = 0;

            if (savedItems) {
                try {
                    const items = JSON.parse(savedItems);
                    itemCount = items.length;
                } catch (e) {
                    console.warn('⚠️ localStorage中的配置解析失败');
                }
            }

            console.log(`📊 当前localStorage中有 ${itemCount} 个动态分析项`);

            // 检查页面中的UI元素
            const container = document.getElementById('dynamicAnalysisContainer');
            const uiItemCount = container ? container.children.length : 0;
            console.log(`🎨 页面UI中有 ${uiItemCount} 个动态分析项`);

            // 如果没有分析项或UI没有正确显示，自动刷新
            if (itemCount === 0 || uiItemCount === 0) {
                console.log('⚠️ 检测到配置问题，自动刷新配置...');
                forceReloadConfig();
            } else {
                console.log('✅ 配置状态正常');
            }
        }, 3000);
    }

    // 添加到全局对象，方便调用
    window.forceReloadConfig = forceReloadConfig;
    window.clearAllCache = clearAllCache;

    // 页面加载完成后检查并自动刷新
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkAndAutoRefresh);
    } else {
        checkAndAutoRefresh();
    }

    console.log('💡 可以在控制台中调用 forceReloadConfig() 来手动重新加载配置');
    console.log('💡 可以在控制台中调用 clearAllCache() 来清除所有缓存');
})();
