// 强制重新加载配置的脚本
(function() {
    console.log('🔄 强制重新加载配置脚本已加载');
    
    // 清除所有相关的localStorage
    function clearAllCache() {
        const keysToRemove = [
            'dynamicAnalysisItems',
            'aiAnalysisSettings',
            'analysisHistory',
            'modelSettings'
        ];
        
        keysToRemove.forEach(key => {
            if (localStorage.getItem(key)) {
                console.log(`🗑️ 清除缓存: ${key}`);
                localStorage.removeItem(key);
            }
        });
    }
    
    // 强制从服务器重新加载配置
    async function forceReloadConfig() {
        try {
            console.log('🔄 开始强制重新加载配置...');
            
            // 清除缓存
            clearAllCache();
            
            // 重新初始化AI设置管理器
            if (window.aiSettingsManager) {
                console.log('🔄 重新初始化AI设置管理器...');
                await window.aiSettingsManager.loadConfigFromServer();
            }
            
            // 重新加载动态分析项
            if (window.chatlogApp && window.chatlogApp.loadDynamicAnalysisItems) {
                console.log('🔄 重新加载动态分析项...');
                window.chatlogApp.loadDynamicAnalysisItems();
            }
            
            console.log('✅ 配置重新加载完成');
            
        } catch (error) {
            console.error('❌ 强制重新加载配置失败:', error);
        }
    }
    
    // 添加到全局对象，方便调用
    window.forceReloadConfig = forceReloadConfig;
    window.clearAllCache = clearAllCache;
    
    // 页面加载完成后自动执行一次
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(forceReloadConfig, 2000);
        });
    } else {
        setTimeout(forceReloadConfig, 2000);
    }
    
    console.log('💡 可以在控制台中调用 forceReloadConfig() 来手动重新加载配置');
    console.log('💡 可以在控制台中调用 clearAllCache() 来清除所有缓存');
})();
