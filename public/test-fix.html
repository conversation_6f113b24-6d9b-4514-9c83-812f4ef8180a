<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置修复测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; border-left: 4px solid #28a745; }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        .info { background-color: #d1ecf1; border-left: 4px solid #17a2b8; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; max-height: 300px; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .analysis-item { background: #f8f9fa; margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
        .analysis-item h4 { margin: 0 0 10px 0; color: #007bff; }
        .analysis-item p { margin: 5px 0; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-ok { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 配置修复测试页面</h1>
        
        <div class="test-section">
            <h3>📊 当前状态检查</h3>
            <button onclick="checkCurrentStatus()">检查当前状态</button>
            <button onclick="testServerConfig()">测试服务器配置</button>
            <button onclick="testLocalStorage()">检查本地存储</button>
            <div id="statusResult"></div>
        </div>

        <div class="test-section">
            <h3>🔄 配置修复操作</h3>
            <button class="btn-warning" onclick="forceSyncConfig()">强制同步配置</button>
            <button class="btn-danger" onclick="clearAllCache()">清除所有缓存</button>
            <button class="btn-success" onclick="reloadPage()">重新加载页面</button>
            <div id="fixResult"></div>
        </div>

        <div class="test-section">
            <h3>📋 分析项详情</h3>
            <button onclick="showAnalysisItems()">显示所有分析项</button>
            <div id="analysisItemsResult"></div>
        </div>

        <div class="test-section">
            <h3>🧪 功能测试</h3>
            <button onclick="testSettingsDialog()">测试设置对话框</button>
            <button onclick="testAnalysisExecution()">测试分析执行</button>
            <div id="functionTestResult"></div>
        </div>

        <div class="test-section">
            <h3>📝 操作日志</h3>
            <button onclick="clearLog()">清除日志</button>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }

        async function checkCurrentStatus() {
            const resultDiv = document.getElementById('statusResult');
            log('🔍 开始检查当前状态...');
            
            let html = '<h4>状态检查结果：</h4>';
            
            // 检查localStorage
            const savedItems = localStorage.getItem('dynamicAnalysisItems');
            let localItemCount = 0;
            let localItems = [];
            
            if (savedItems) {
                try {
                    localItems = JSON.parse(savedItems);
                    localItemCount = localItems.length;
                    html += `<p><span class="status-indicator status-ok"></span>localStorage: ${localItemCount} 个分析项</p>`;
                    log(`✅ localStorage中有 ${localItemCount} 个分析项`);
                } catch (e) {
                    html += `<p><span class="status-indicator status-error"></span>localStorage: 解析错误</p>`;
                    log('❌ localStorage解析错误');
                }
            } else {
                html += `<p><span class="status-indicator status-error"></span>localStorage: 空</p>`;
                log('❌ localStorage为空');
            }
            
            // 检查AI设置管理器
            if (window.aiSettingsManager) {
                const managerItems = window.aiSettingsManager.dynamicAnalysisItems || [];
                html += `<p><span class="status-indicator status-ok"></span>AI设置管理器: ${managerItems.length} 个分析项</p>`;
                log(`✅ AI设置管理器中有 ${managerItems.length} 个分析项`);
            } else {
                html += `<p><span class="status-indicator status-error"></span>AI设置管理器: 未初始化</p>`;
                log('❌ AI设置管理器未初始化');
            }
            
            // 检查UI容器
            const container = document.getElementById('dynamicAnalysisContainer');
            if (container) {
                const uiItemCount = container.children.length;
                html += `<p><span class="status-indicator ${uiItemCount > 0 ? 'status-ok' : 'status-error'}"></span>UI显示: ${uiItemCount} 个分析项</p>`;
                log(`${uiItemCount > 0 ? '✅' : '❌'} UI中显示 ${uiItemCount} 个分析项`);
            } else {
                html += `<p><span class="status-indicator status-error"></span>UI容器: 不存在</p>`;
                log('❌ UI容器不存在');
            }
            
            resultDiv.innerHTML = html;
        }

        async function testServerConfig() {
            log('🔄 测试服务器配置...');
            
            try {
                const response = await fetch('/api/get-analysis-config');
                const data = await response.json();
                
                if (data.success && data.config && data.config.dynamicAnalysisItems) {
                    const items = data.config.dynamicAnalysisItems;
                    log(`✅ 服务器配置正常，返回 ${items.length} 个分析项`);
                    return items;
                } else {
                    log('❌ 服务器配置格式错误');
                    return null;
                }
            } catch (error) {
                log(`❌ 服务器配置测试失败: ${error.message}`);
                return null;
            }
        }

        function testLocalStorage() {
            log('🔄 测试本地存储...');
            
            const keys = ['dynamicAnalysisItems', 'aiAnalysisSettings', 'analysisHistory'];
            keys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    try {
                        const parsed = JSON.parse(value);
                        log(`✅ ${key}: ${Array.isArray(parsed) ? parsed.length + ' 个项目' : '已存在'}`);
                    } catch (e) {
                        log(`❌ ${key}: 解析错误`);
                    }
                } else {
                    log(`⚠️ ${key}: 空`);
                }
            });
        }

        async function forceSyncConfig() {
            const resultDiv = document.getElementById('fixResult');
            log('🔄 开始强制同步配置...');
            
            try {
                if (window.forceSyncConfig) {
                    const success = await window.forceSyncConfig();
                    if (success) {
                        resultDiv.innerHTML = '<div class="success"><h4>✅ 配置同步成功</h4><p>所有分析项已重新加载</p></div>';
                        log('✅ 配置同步成功');
                    } else {
                        resultDiv.innerHTML = '<div class="error"><h4>❌ 配置同步失败</h4><p>请查看控制台了解详情</p></div>';
                        log('❌ 配置同步失败');
                    }
                } else {
                    resultDiv.innerHTML = '<div class="warning"><h4>⚠️ 同步功能不可用</h4><p>请刷新页面重试</p></div>';
                    log('⚠️ forceSyncConfig函数不存在');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><h4>❌ 同步过程出错</h4><p>${error.message}</p></div>`;
                log(`❌ 同步过程出错: ${error.message}`);
            }
        }

        function clearAllCache() {
            log('🗑️ 清除所有缓存...');
            
            const keys = ['dynamicAnalysisItems', 'aiAnalysisSettings', 'analysisHistory', 'modelSettings'];
            keys.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    log(`🗑️ 已清除: ${key}`);
                }
            });
            
            const resultDiv = document.getElementById('fixResult');
            resultDiv.innerHTML = '<div class="info"><h4>🗑️ 缓存已清除</h4><p>建议刷新页面重新加载配置</p></div>';
        }

        function reloadPage() {
            log('🔄 重新加载页面...');
            window.location.reload();
        }

        async function showAnalysisItems() {
            const resultDiv = document.getElementById('analysisItemsResult');
            log('📋 显示分析项详情...');
            
            try {
                const response = await fetch('/api/get-analysis-config');
                const data = await response.json();
                
                if (data.success && data.config && data.config.dynamicAnalysisItems) {
                    const items = data.config.dynamicAnalysisItems;
                    let html = `<h4>📋 共 ${items.length} 个分析项：</h4>`;
                    
                    items.forEach((item, index) => {
                        html += `
                            <div class="analysis-item">
                                <h4>${index + 1}. ${item.displayName}</h4>
                                <p><strong>ID:</strong> ${item.id}</p>
                                <p><strong>群名称:</strong> ${item.groupName}</p>
                                <p><strong>时间范围:</strong> ${item.timeRange}</p>
                                <p><strong>提示词:</strong> ${item.prompt.substring(0, 100)}...</p>
                            </div>
                        `;
                    });
                    
                    resultDiv.innerHTML = html;
                    log(`✅ 显示了 ${items.length} 个分析项的详情`);
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ 无法获取分析项配置</div>';
                    log('❌ 无法获取分析项配置');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 获取分析项失败: ${error.message}</div>`;
                log(`❌ 获取分析项失败: ${error.message}`);
            }
        }

        function testSettingsDialog() {
            log('🧪 测试设置对话框...');
            
            if (window.aiSettingsManager && window.aiSettingsManager.openSettings) {
                // 测试打开第一个分析项的设置
                const items = window.aiSettingsManager.dynamicAnalysisItems;
                if (items && items.length > 0) {
                    window.aiSettingsManager.openSettings(items[0].id);
                    log(`✅ 已打开 ${items[0].displayName} 的设置对话框`);
                } else {
                    log('❌ 没有可用的分析项');
                }
            } else {
                log('❌ AI设置管理器未初始化或openSettings方法不存在');
            }
        }

        function testAnalysisExecution() {
            log('🧪 测试分析执行...');
            
            if (window.aiSettingsManager && window.aiSettingsManager.dynamicAnalysisItems) {
                const items = window.aiSettingsManager.dynamicAnalysisItems;
                if (items && items.length > 0) {
                    const item = items[0];
                    log(`🚀 模拟执行分析: ${item.displayName}`);
                    
                    if (window.chatlogApp && window.chatlogApp.startAIAnalysis) {
                        // 这里只是测试，不实际执行
                        log(`✅ 分析执行功能可用`);
                    } else {
                        log('❌ 主应用未初始化或startAIAnalysis方法不存在');
                    }
                } else {
                    log('❌ 没有可用的分析项');
                }
            } else {
                log('❌ AI设置管理器未初始化');
            }
        }

        // 页面加载时自动检查状态
        window.onload = function() {
            log('🚀 测试页面已加载');
            setTimeout(checkCurrentStatus, 1000);
        };
    </script>
</body>
</html>
