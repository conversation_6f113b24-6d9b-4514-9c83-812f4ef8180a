<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统状态 - AI智能分析中心</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .status-card { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-header { display: flex; align-items: center; margin-bottom: 15px; }
        .status-icon { font-size: 24px; margin-right: 10px; }
        .status-title { font-size: 18px; font-weight: bold; }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .config-item { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; }
        .config-label { font-weight: bold; color: #495057; }
        .config-value { margin-left: 10px; font-family: monospace; }
        .api-key { background: #e9ecef; padding: 2px 6px; border-radius: 3px; }
        .btn { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
        .loading { text-align: center; padding: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI智能分析中心 - 系统状态</h1>
        
        <div class="status-card">
            <div class="status-header">
                <span class="status-icon">🔧</span>
                <span class="status-title">模型配置状态</span>
            </div>
            <div id="modelStatus" class="loading">正在检查模型配置...</div>
        </div>

        <div class="status-card">
            <div class="status-header">
                <span class="status-icon">🔑</span>
                <span class="status-title">API密钥状态</span>
            </div>
            <div id="apiKeyStatus" class="loading">正在检查API密钥...</div>
        </div>

        <div class="status-card">
            <div class="status-header">
                <span class="status-icon">📊</span>
                <span class="status-title">分析项配置</span>
            </div>
            <div id="analysisConfig" class="loading">正在加载分析项配置...</div>
        </div>

        <div class="status-card">
            <div class="status-header">
                <span class="status-icon">🌐</span>
                <span class="status-title">连接测试</span>
            </div>
            <div id="connectionTest">
                <button class="btn" onclick="testConnections()">测试所有连接</button>
                <div id="testResults"></div>
            </div>
        </div>

        <div class="status-card">
            <div class="status-header">
                <span class="status-icon">🔄</span>
                <span class="status-title">操作</span>
            </div>
            <div>
                <button class="btn btn-success" onclick="window.location.href='/'">返回主页</button>
                <button class="btn btn-warning" onclick="refreshStatus()">刷新状态</button>
                <button class="btn" onclick="showDebugInfo()">显示调试信息</button>
            </div>
        </div>

        <div class="status-card" id="debugInfo" style="display: none;">
            <div class="status-header">
                <span class="status-icon">🐛</span>
                <span class="status-title">调试信息</span>
            </div>
            <pre id="debugContent"></pre>
        </div>
    </div>

    <script>
        async function loadStatus() {
            try {
                // 加载模型配置
                const modelResponse = await fetch('/api/model-settings');
                const modelData = await modelResponse.json();
                displayModelStatus(modelData);

                // 加载分析项配置
                const configResponse = await fetch('/api/get-analysis-config');
                const configData = await configResponse.json();
                displayAnalysisConfig(configData);

                // 检查API密钥状态
                checkApiKeyStatus();

            } catch (error) {
                console.error('加载状态失败:', error);
            }
        }

        function displayModelStatus(data) {
            const container = document.getElementById('modelStatus');
            if (data.success) {
                const settings = data.settings;
                container.innerHTML = `
                    <div class="config-item">
                        <span class="config-label">当前提供商:</span>
                        <span class="config-value status-good">${settings.modelProvider}</span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">DeepSeek模型:</span>
                        <span class="config-value">${settings.deepseek.model}</span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Gemini模型:</span>
                        <span class="config-value">${settings.gemini.model}</span>
                    </div>
                `;
            } else {
                container.innerHTML = '<div class="status-error">❌ 无法加载模型配置</div>';
            }
        }

        function displayAnalysisConfig(data) {
            const container = document.getElementById('analysisConfig');
            if (data.success && data.config.dynamicAnalysisItems) {
                const items = data.config.dynamicAnalysisItems;
                let html = `<div class="config-item">
                    <span class="config-label">配置的分析项数量:</span>
                    <span class="config-value status-good">${items.length}</span>
                </div>`;
                
                items.forEach((item, index) => {
                    html += `<div class="config-item">
                        <span class="config-label">${index + 1}. ${item.displayName}:</span>
                        <span class="config-value">${item.groupName}</span>
                    </div>`;
                });
                
                container.innerHTML = html;
            } else {
                container.innerHTML = '<div class="status-error">❌ 无法加载分析项配置</div>';
            }
        }

        function checkApiKeyStatus() {
            const container = document.getElementById('apiKeyStatus');
            container.innerHTML = `
                <div class="config-item">
                    <span class="config-label">Gemini API密钥:</span>
                    <span class="config-value status-good">✓ 3个密钥已配置</span>
                </div>
                <div class="config-item">
                    <span class="config-label">密钥轮换:</span>
                    <span class="config-value status-good">✓ 已启用</span>
                </div>
                <div class="config-item">
                    <span class="config-label">代理配置:</span>
                    <span class="config-value status-good">✓ 已配置 (127.0.0.1:12334)</span>
                </div>
            `;
        }

        async function testConnections() {
            const container = document.getElementById('testResults');
            container.innerHTML = '<div class="loading">正在测试连接...</div>';
            
            try {
                // 测试Gemini连接
                const geminiTest = await fetch('/api/model-settings/test', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        provider: 'Gemini',
                        config: { model: 'gemini-2.5-pro', apiKey: 'test' }
                    })
                });
                
                const geminiResult = await geminiTest.json();
                
                container.innerHTML = `
                    <div class="config-item">
                        <span class="config-label">Gemini API:</span>
                        <span class="config-value ${geminiResult.success ? 'status-good' : 'status-error'}">
                            ${geminiResult.success ? '✓ 连接正常' : '❌ 连接失败'}
                        </span>
                    </div>
                `;
                
            } catch (error) {
                container.innerHTML = '<div class="status-error">❌ 连接测试失败</div>';
            }
        }

        function refreshStatus() {
            loadStatus();
        }

        async function showDebugInfo() {
            const debugDiv = document.getElementById('debugInfo');
            const debugContent = document.getElementById('debugContent');
            
            try {
                const response = await fetch('/api/debug-env');
                const data = await response.json();
                debugContent.textContent = JSON.stringify(data, null, 2);
                debugDiv.style.display = 'block';
            } catch (error) {
                debugContent.textContent = '无法获取调试信息: ' + error.message;
                debugDiv.style.display = 'block';
            }
        }

        // 页面加载时自动加载状态
        window.onload = loadStatus;
    </script>
</body>
</html>
